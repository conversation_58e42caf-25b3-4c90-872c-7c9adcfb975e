/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export', // Enable static exports
  trailingSlash: false, // Disable trailing slashes to prevent .txt files
  distDir: 'out', // Output directory for the build
  images: {
    unoptimized: true, // Disable image optimization for static export
  },

  // Disable ESLint during build
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Disable TypeScript type checking during build
  typescript: {
    ignoreBuildErrors: true,
  },

  // Disable experimental features that might cause .txt files
  experimental: {
    optimizePackageImports: [],
  },
};

module.exports = nextConfig;
