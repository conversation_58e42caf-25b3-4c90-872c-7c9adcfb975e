'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, Upload, FileText, XCircle, Download } from 'lucide-react';
import apiService from '@/lib/api';
import { toast } from 'sonner';

// Types for bank statement functionality
interface BankStatementEntry {
  date: string;
  description: string;
  amount: number;
  type: 'income' | 'expense';
  category_id?: string;
  account_id?: string;
  isSelected?: boolean;
}

interface BankStatementUploadResponse {
  entries: BankStatementEntry[];
}

interface Category {
  id: string;
  name: string;
  type: string;
  icon: string;
}

interface Account {
  id: string;
  name: string;
  type: string;
  balance: number;
  currency: string;
}

export default function BankStatementsPage() {
  const [isUploading, setIsUploading] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [entries, setEntries] = useState<BankStatementEntry[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [selectedAccountId, setSelectedAccountId] = useState<string>('');
  const [error, setError] = useState<string>('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Load categories and accounts on component mount
  useEffect(() => {
    loadCategoriesAndAccounts();
  }, []);

  const loadCategoriesAndAccounts = async () => {
    try {
      console.log('Loading categories and accounts...');
      const [categoriesData, accountsData] = await Promise.all([
        apiService.categories.getAll(),
        apiService.accounts.getAll()
      ]);
      console.log('Categories data:', categoriesData);
      console.log('Accounts data:', accountsData);
      setCategories(categoriesData || []);
      setAccounts(accountsData || []);

      // Set the first account as default if available
      if (accountsData && accountsData.length > 0) {
        setSelectedAccountId(accountsData[0].id);
      }
    } catch (error) {
      console.error('Error loading categories and accounts:', error);
      setError('Failed to load categories and accounts: ' + error);
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.name.toLowerCase().endsWith('.pdf')) {
      setError('Please select a PDF file');
      return;
    }

    setIsUploading(true);
    setError('');

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response: BankStatementUploadResponse = await apiService.bankStatements.upload(formData);

      if (response.entries && response.entries.length > 0) {
        const entriesWithSelection = response.entries.map(entry => ({
          ...entry,
          isSelected: true // Select all entries by default
        }));
        setEntries(entriesWithSelection);
        toast.success(`Successfully parsed ${response.entries.length} transactions from the PDF`);
      } else {
        setError('No transactions found in the PDF file');
      }
    } catch (error) {
      console.error('Upload error:', error);
      setError(typeof error === 'string' ? error : 'Failed to upload and parse the PDF file');
      toast.error('Failed to upload bank statement');
    } finally {
      setIsUploading(false);
      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const toggleEntrySelection = (index: number) => {
    setEntries(prev => prev.map((entry, i) =>
      i === index ? { ...entry, isSelected: !entry.isSelected } : entry
    ));
  };

  const toggleSelectAll = () => {
    const allSelected = entries.every(entry => entry.isSelected);
    setEntries(prev => prev.map(entry => ({ ...entry, isSelected: !allSelected })));
  };

  const handleImportSelected = async () => {
    const selectedEntries = entries.filter(entry => entry.isSelected);

    if (selectedEntries.length === 0) {
      toast.error('Please select at least one transaction to import');
      return;
    }

    if (!selectedAccountId) {
      toast.error('Please select an account for the transactions');
      return;
    }

    setIsImporting(true);
    setError('');

    try {
      const importData = selectedEntries.map(entry => ({
        date: entry.date,
        description: entry.description,
        amount: entry.amount,
        type: entry.type,
        category_id: entry.category_id,
        account_id: selectedAccountId // Use the selected account for all transactions
      }));

      await apiService.bankStatements.import(importData);

      toast.success(`Successfully imported ${selectedEntries.length} transactions to ${getAccountName(selectedAccountId)}`);

      // Clear entries after successful import
      setEntries([]);
    } catch (error) {
      console.error('Import error:', error);
      setError(typeof error === 'string' ? error : 'Failed to import transactions');
      toast.error('Failed to import transactions');
    } finally {
      setIsImporting(false);
    }
  };

  const getCategoryName = (categoryId?: string) => {
    if (!categoryId) return 'Auto-assigned';
    const category = categories.find(c => c.id === categoryId);
    return category ? category.name : 'Unknown Category';
  };

  const getAccountName = (accountId?: string) => {
    if (!accountId) return 'Auto-assigned';
    const account = accounts.find(a => a.id === accountId);
    return account ? account.name : 'Unknown Account';
  };

  const selectedCount = entries.filter(entry => entry.isSelected).length;
  const totalAmount = entries
    .filter(entry => entry.isSelected)
    .reduce((sum, entry) => sum + (entry.type === 'expense' ? -entry.amount : entry.amount), 0);

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Bank Statements</h1>
        <p className="text-muted-foreground">
          Upload your bank statement PDF to automatically extract transactions with smart category assignment and account selection
        </p>
      </div>

      {/* Upload Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Upload Bank Statement
          </CardTitle>
          <CardDescription>
            Select a PDF file and target account to extract transactions with auto-assigned categories
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="grid w-full items-center gap-1.5">
              <Label htmlFor="pdf-file">PDF File</Label>
              <Input
                ref={fileInputRef}
                id="pdf-file"
                type="file"
                accept=".pdf"
                onChange={handleFileUpload}
                disabled={isUploading}
              />
            </div>

            <div className="grid w-full items-center gap-1.5">
              <Label htmlFor="account-select">Target Account</Label>
              <Select
                value={selectedAccountId}
                onValueChange={setSelectedAccountId}
                disabled={isUploading || accounts.length === 0}
              >
                <SelectTrigger id="account-select">
                  <SelectValue placeholder="Select an account" />
                </SelectTrigger>
                <SelectContent>
                  {accounts.map((account) => (
                    <SelectItem key={account.id} value={account.id}>
                      <div className="flex items-center justify-between w-full">
                        <span>{account.name}</span>
                        <span className="text-xs text-muted-foreground ml-2">
                          {account.balance.toFixed(2)} {account.currency}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {accounts.length === 0 && (
                <p className="text-xs text-muted-foreground">
                  No accounts available. Please create an account first.
                </p>
              )}
            </div>
          </div>

          {error && (
            <Alert variant="destructive">
              <XCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {isUploading && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              Uploading and parsing PDF...
            </div>
          )}
        </CardContent>
      </Card>

      {/* Transactions Preview */}
      {entries.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Extracted Transactions
                </CardTitle>
                <CardDescription>
                  Review and select transactions to import ({selectedCount} of {entries.length} selected)
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={toggleSelectAll}
                >
                  {entries.every(entry => entry.isSelected) ? 'Deselect All' : 'Select All'}
                </Button>
                <Button
                  onClick={handleImportSelected}
                  disabled={selectedCount === 0 || isImporting}
                  className="flex items-center gap-2"
                >
                  {isImporting ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Download className="h-4 w-4" />
                  )}
                  Import Selected ({selectedCount})
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {selectedCount > 0 && (
              <div className="mb-4 p-3 bg-muted rounded-lg">
                <div className="text-sm font-medium">
                  Selected: {selectedCount} transactions
                </div>
                <div className={`text-sm ${totalAmount >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  Total: {totalAmount >= 0 ? '+' : ''}{totalAmount.toFixed(2)} TL
                </div>
              </div>
            )}

            <div className="space-y-2 max-h-96 overflow-y-auto">
              {entries.map((entry, index) => (
                <div
                  key={index}
                  className={`flex items-center space-x-3 p-3 border rounded-lg ${
                    entry.isSelected ? 'bg-muted/50 border-primary' : 'bg-background'
                  }`}
                >
                  <Checkbox
                    checked={entry.isSelected}
                    onCheckedChange={() => toggleEntrySelection(index)}
                  />

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium truncate">{entry.description}</p>
                      <div className="flex items-center gap-2">
                        <Badge variant={entry.type === 'expense' ? 'destructive' : 'default'}>
                          {entry.type === 'expense' ? '-' : '+'}{entry.amount.toFixed(2)} TL
                        </Badge>
                      </div>
                    </div>

                    <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
                      <span>{entry.date}</span>
                      <span>Category: {getCategoryName(entry.category_id)} (Auto-assigned)</span>
                      <span>Account: {getAccountName(selectedAccountId)} (Selected)</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
