'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function BankStatementsPage() {
  const [testMessage, setTestMessage] = useState('Bank Statements page is loading...');

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Bank Statements</h1>
        <p className="text-muted-foreground">
          Upload your bank statement PDF to automatically extract and import transactions
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Test Page</CardTitle>
          <CardDescription>
            This is a simplified version to test if the page loads correctly
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p>{testMessage}</p>
          <button
            onClick={() => setTestMessage('Button clicked! Page is working.')}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Test Button
          </button>
        </CardContent>
      </Card>
    </div>
  );
}
