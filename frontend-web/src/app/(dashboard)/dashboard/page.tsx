'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  ArrowDownIcon,
  ArrowUpIcon,
  CreditCard,
  DollarSign,
  Users,
  Wallet,
  BarChart3,
  PieChart,
  TrendingDown,
  TrendingUp
} from 'lucide-react';
import { format } from 'date-fns';
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>
} from 'recharts';
import apiService from '@/lib/api';

// Define types for our data
interface SummaryData {
  totalIncome: number;
  totalExpense: number;
  netBalance: number;
  topExpenseCategories: { category: string; amount: number }[];
  topIncomeCategories: { category: string; amount: number }[];
}

interface MonthlyData {
  month: string;
  income: number;
  expense: number;
}

interface CategoryData {
  name: string;
  value: number;
}

// Colors for pie chart
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D', '#FFC658', '#8DD1E1'];

export default function DashboardPage() {
  const [summaryData, setSummaryData] = useState<SummaryData | null>(null);
  const [monthlyData, setMonthlyData] = useState<MonthlyData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('month');

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  // Get date range based on selected period
  const getDateRange = () => {
    const now = new Date();
    let startDate = new Date();

    if (selectedPeriod === 'month') {
      startDate.setMonth(now.getMonth() - 1);
    } else if (selectedPeriod === 'quarter') {
      startDate.setMonth(now.getMonth() - 3);
    } else if (selectedPeriod === 'year') {
      startDate.setFullYear(now.getFullYear() - 1);
    }

    return {
      start_date: format(startDate, 'yyyy-MM-dd'),
      end_date: format(now, 'yyyy-MM-dd'),
    };
  };

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        const dateRange = getDateRange();

        // Fetch summary data
        const summary = await apiService.reports.getSummary(dateRange);
        console.log('Summary data:', summary); // Debug log

        // Initialize with default values if data is missing
        const processedSummary: SummaryData = {
          totalIncome: summary?.totalIncome || 0,
          totalExpense: summary?.totalExpense || 0,
          netBalance: summary?.netBalance || 0,
          topExpenseCategories: summary?.topExpenseCategories || [],
          topIncomeCategories: summary?.topIncomeCategories || []
        };

        setSummaryData(processedSummary);

        // Fetch monthly data
        const monthly = await apiService.reports.getMonthlyReport({
          year: new Date().getFullYear(),
        });
        console.log('Monthly data:', monthly); // Debug log

        setMonthlyData(monthly?.months || []);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        // Set default values on error
        setSummaryData({
          totalIncome: 0,
          totalExpense: 0,
          netBalance: 0,
          topExpenseCategories: [],
          topIncomeCategories: []
        });
        setMonthlyData([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [selectedPeriod]);

  // Prepare data for pie charts
  const expenseCategoriesData: CategoryData[] = summaryData?.topExpenseCategories?.map(item => ({
    name: item.category,
    value: item.amount,
  })) || [];

  const incomeCategoriesData: CategoryData[] = summaryData?.topIncomeCategories?.map(item => ({
    name: item.category,
    value: item.amount,
  })) || [];

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
          <p className="text-muted-foreground">
            Overview of your financial activities
          </p>
        </div>
        <Tabs defaultValue="month" className="w-full md:w-auto" onValueChange={setSelectedPeriod}>
          <TabsList>
            <TabsTrigger value="month">Month</TabsTrigger>
            <TabsTrigger value="quarter">Quarter</TabsTrigger>
            <TabsTrigger value="year">Year</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      ) : (
        <>
          {/* Summary Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Income</CardTitle>
                <TrendingUp className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(summaryData?.totalIncome || 0)}</div>
                <p className="text-xs text-muted-foreground">
                  For the selected period
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
                <TrendingDown className="h-4 w-4 text-red-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(summaryData?.totalExpense || 0)}</div>
                <p className="text-xs text-muted-foreground">
                  For the selected period
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Net Balance</CardTitle>
                <Wallet className="h-4 w-4 text-blue-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(summaryData?.netBalance || 0)}</div>
                <p className="text-xs text-muted-foreground">
                  Income minus expenses
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Savings Rate</CardTitle>
                <DollarSign className="h-4 w-4 text-yellow-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {summaryData?.totalIncome && summaryData.totalIncome > 0
                    ? `${Math.round((summaryData.netBalance / summaryData.totalIncome) * 100)}%`
                    : '0%'}
                </div>
                <p className="text-xs text-muted-foreground">
                  Net balance / Total income
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Charts */}
          <div className="grid gap-4 md:grid-cols-2">
            {/* Monthly Income vs Expenses */}
            <Card className="col-span-2 md:col-span-1">
              <CardHeader>
                <CardTitle>Monthly Income vs Expenses</CardTitle>
                <CardDescription>
                  Comparison of income and expenses over the past months
                </CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={monthlyData}
                    margin={{
                      top: 5,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                    <Legend />
                    <Bar dataKey="income" fill="#4ade80" name="Income" />
                    <Bar dataKey="expense" fill="#f87171" name="Expense" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Expense Categories */}
            <Card className="col-span-2 md:col-span-1">
              <CardHeader>
                <CardTitle>Expense Categories</CardTitle>
                <CardDescription>
                  Breakdown of expenses by category
                </CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <RePieChart>
                    <Pie
                      data={expenseCategoriesData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {expenseCategoriesData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                    <Legend />
                  </RePieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </>
      )}
    </div>
  );
}
