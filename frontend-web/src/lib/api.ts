import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

// Create axios instance with base URL
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8008/api/v1',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    // Get token from localStorage in client-side
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('auth_token');
      if (token && config.headers) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    // Log response for debugging
    console.log('API Response:', response);

    // Extract data from response if it exists
    if (response.data && response.data.data) {
      return response.data.data;
    }

    // For empty responses, return an empty object
    if (!response.data) {
      console.warn('Empty response data');
      return {};
    }

    return response.data;
  },
  (error: AxiosError) => {
    // Log error for debugging
    console.error('API Error:', error);

    // Handle 401 Unauthorized errors (token expired or invalid)
    if (error.response?.status === 401) {
      // Clear auth data
      if (typeof window !== 'undefined') {
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user');
        // Redirect to login page if not already there
        if (window.location.pathname !== '/login') {
          window.location.href = '/login';
        }
      }
    }

    // Return error message
    const errorMessage =
      error.response?.data?.error ||
      error.message ||
      'An unknown error occurred';

    return Promise.reject(errorMessage);
  }
);

// API service with typed methods
const apiService = {
  // Auth endpoints
  auth: {
    login: (credentials: { username: string; password: string }) =>
      api.post('/login', credentials),

    register: (userData: {
      username: string;
      email: string;
      password: string;
      name: string;
    }) => api.post('/register', userData),

    getProfile: () => api.get('/me'),

    logout: () => api.post('/logout'),
  },

  // Categories endpoints
  categories: {
    getAll: (type?: string) =>
      api.get('/categories', { params: { type } }),

    getById: (id: string) =>
      api.get(`/categories/${id}`),

    create: (data: {
      name: string;
      type: string;
      icon: string;
    }) => api.post('/categories', data),

    update: (id: string, data: {
      name: string;
      type: string;
      icon: string;
    }) => api.put(`/categories/${id}`, data),

    delete: (id: string) =>
      api.delete(`/categories/${id}`),
  },

  // Accounts endpoints
  accounts: {
    getAll: () => api.get('/accounts'),

    getById: (id: string) =>
      api.get(`/accounts/${id}`),

    create: (data: {
      name: string;
      type: string;
      balance: number;
      currency: string;
    }) => api.post('/accounts', data),

    update: (id: string, data: {
      name: string;
      type: string;
      balance: number;
      currency: string;
    }) => api.put(`/accounts/${id}`, data),

    delete: (id: string) =>
      api.delete(`/accounts/${id}`),
  },

  // Transactions endpoints
  transactions: {
    getAll: (params?: {
      type?: string;
      start_date?: string;
      end_date?: string;
      category_id?: string;
      payment_method?: string;
      account_id?: string;
      min_amount?: number;
      max_amount?: number;
      search?: string;
      page?: number;
      limit?: number;
    }) => api.get('/transactions', { params }),

    getById: (id: string) =>
      api.get(`/transactions/${id}`),

    create: (data: {
      title: string;
      type: string;
      amount: number;
      currency: string;
      category_id: string;
      payment_method: string;
      account_id: string;
      note?: string;
      transaction_date: string;
      location?: string;
    }) => api.post('/transactions', data),

    update: (id: string, data: {
      title?: string;
      type?: string;
      amount?: number;
      currency?: string;
      category_id?: string;
      payment_method?: string;
      account_id?: string;
      note?: string;
      transaction_date?: string;
      location?: string;
    }) => api.put(`/transactions/${id}`, data),

    delete: (id: string) =>
      api.delete(`/transactions/${id}`),
  },

  // Reports endpoints
  reports: {
    getSummary: async (params: {
      start_date: string;
      end_date: string;
    }) => {
      try {
        const response = await api.get('/reports/summary', { params });
        // Return default structure if response is empty or invalid
        if (!response || typeof response !== 'object') {
          console.warn('Invalid summary response, using default values');
          return {
            totalIncome: 0,
            totalExpense: 0,
            netBalance: 0,
            topExpenseCategories: [],
            topIncomeCategories: []
          };
        }
        // Backend wraps data in a 'data' field
        return response.data || response;
      } catch (error) {
        console.error('Error fetching summary:', error);
        // Return default structure on error
        return {
          totalIncome: 0,
          totalExpense: 0,
          netBalance: 0,
          topExpenseCategories: [],
          topIncomeCategories: []
        };
      }
    },

    getCategoryBreakdown: async (params: {
      start_date: string;
      end_date: string;
    }) => {
      try {
        const response = await api.get('/reports/category-breakdown', { params });
        if (!response || typeof response !== 'object') {
          return {
            expenseCategories: [],
            incomeCategories: []
          };
        }
        // Backend wraps data in a 'data' field
        return response.data || response;
      } catch (error) {
        console.error('Error fetching category breakdown:', error);
        return {
          expenseCategories: [],
          incomeCategories: []
        };
      }
    },

    getMonthlyReport: async (params?: {
      year?: number;
    }) => {
      try {
        const response = await api.get('/reports/monthly', { params });
        if (!response || typeof response !== 'object') {
          return { months: [] };
        }
        // Backend wraps data in a 'data' field
        return response.data || response;
      } catch (error) {
        console.error('Error fetching monthly report:', error);
        return { months: [] };
      }
    },

    getLocationSummary: async (params: {
      start_date: string;
      end_date: string;
    }) => {
      try {
        const response = await api.get('/reports/location-summary', { params });
        if (!response || typeof response !== 'object') {
          return { locations: [] };
        }
        // Backend wraps data in a 'data' field
        return response.data || response;
      } catch (error) {
        console.error('Error fetching location summary:', error);
        return { locations: [] };
      }
    },
  },

  // Bank statements endpoints
  bankStatements: {
    upload: (formData: FormData) =>
      api.post('/bank-statements/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }),

    import: (entries: {
      date: string;
      description: string;
      amount: number;
      type: string;
      category_id?: string;
      account_id?: string;
    }[]) =>
      api.post('/bank-statements/import', entries),
  },
};

export default apiService;
