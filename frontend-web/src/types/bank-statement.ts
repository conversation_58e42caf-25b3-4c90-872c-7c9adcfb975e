export interface BankStatementEntry {
  date: string;
  description: string;
  amount: number;
  type: 'income' | 'expense';
  category_id?: string;
  account_id?: string;
  isSelected?: boolean;
}

export interface BankStatementUploadResponse {
  entries: BankStatementEntry[];
}

export interface BankStatementImportRequest {
  date: string;
  description: string;
  amount: number;
  type: 'income' | 'expense';
  category_id?: string;
  account_id?: string;
}

export interface BankStatementImportResponse {
  transactions: Transaction[];
}

export interface Transaction {
  id: string;
  title: string;
  type: 'income' | 'expense';
  amount: number;
  currency: string;
  category_id: string;
  payment_method: string;
  account_id: string;
  note?: string;
  transaction_date: string;
  location?: string;
  created_at: string;
  updated_at: string;
}

export interface Category {
  id: string;
  name: string;
  type: 'income' | 'expense';
  icon: string;
  created_at: string;
  updated_at: string;
}

export interface Account {
  id: string;
  name: string;
  type: string;
  balance: number;
  currency: string;
  created_at: string;
  updated_at: string;
}
