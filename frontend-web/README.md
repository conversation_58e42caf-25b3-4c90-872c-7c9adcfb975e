# Finance Notebook - Modern Web Frontend

A modern web frontend for the Finance Notebook application, built with Next.js, React, TypeScript, and Tailwind CSS.

## Features

- **Modern UI**: Built with Next.js, React, TypeScript, and Tailwind CSS
- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Authentication**: User registration, login, and profile management
- **Dashboard**: Overview of financial activities with charts and summaries
- **Transactions**: Create, view, edit, and delete income and expense transactions
- **Categories**: Manage income and expense categories
- **Accounts**: Manage financial accounts
- **Reports**: Visualize financial data with charts and reports

## Tech Stack

- **Next.js**: React framework for server-side rendering and static site generation
- **React**: JavaScript library for building user interfaces
- **TypeScript**: Typed superset of JavaScript
- **Tailwind CSS**: Utility-first CSS framework
- **Shadcn UI**: Beautiful, accessible UI components
- **React Query**: Data fetching, caching, and state management
- **Zustand**: Lightweight global state management
- **Recharts**: Composable charting library
- **Axios**: Promise-based HTTP client
- **React Hook Form**: Form handling with validation
- **Zod**: Schema validation

## Getting Started

### Prerequisites

- Node.js 18.x or later
- npm or yarn

### Installation

1. Clone the repository
2. Navigate to the frontend-web directory
3. Install dependencies:

```bash
npm install
# or
yarn install
```

### Development

Run the development server:

```bash
npm run dev
# or
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser to see the application.

### Building for Production

Build the application for production:

```bash
npm run build
# or
yarn build
```

Start the production server:

```bash
npm run start
# or
yarn start
```

## Environment Variables

Create a `.env.local` file in the root directory with the following variables:

```
NEXT_PUBLIC_API_URL=http://localhost:8008/api/v1
```

## Backend Integration

This frontend is designed to work with the Finance Notebook backend API. Make sure the backend server is running and accessible at the URL specified in the `NEXT_PUBLIC_API_URL` environment variable.
