package server

import (
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"path"
	"time"

	"github.com/Depado/ginprom"
	"github.com/NocyTech/fin_notebook/app/api/routes"
	"github.com/NocyTech/fin_notebook/docs"
	"github.com/NocyTech/fin_notebook/pkg/config"
	"github.com/NocyTech/fin_notebook/pkg/database"
	"github.com/NocyTech/fin_notebook/pkg/domains/account"
	"github.com/NocyTech/fin_notebook/pkg/domains/auth"
	"github.com/NocyTech/fin_notebook/pkg/domains/bank_statement"
	"github.com/NocyTech/fin_notebook/pkg/domains/category"
	"github.com/NocyTech/fin_notebook/pkg/domains/recurring"
	"github.com/NocyTech/fin_notebook/pkg/domains/report"
	"github.com/NocyTech/fin_notebook/pkg/domains/transaction"
	"github.com/NocyTech/fin_notebook/pkg/domains/version"
	"github.com/NocyTech/fin_notebook/pkg/embed"
	"github.com/NocyTech/fin_notebook/pkg/middleware"
	"github.com/NocyTech/fin_notebook/pkg/state"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
)

var (
	swaggerUser string
	swaggerPass string
)

func LaunchHttpServer(appc config.App, allows config.Allows) {
	log.Println("Starting HTTP Server...")
	gin.SetMode(gin.ReleaseMode)

	app := gin.New()
	app.Use(gin.LoggerWithFormatter(func(log gin.LogFormatterParams) string {
		return fmt.Sprintf("[%s] - %s \"%s %s %s %d %s\"\n",
			log.TimeStamp.Format("2006-01-02 15:04:05"),
			log.ClientIP,
			log.Method,
			log.Path,
			log.Request.Proto,
			log.StatusCode,
			log.Latency,
		)
	}))
	app.Use(gin.Recovery())
	app.Use(otelgin.Middleware(appc.Name))
	app.Use(middleware.ClaimIp())

	//app.Use(middleware.Secure())
	app.Use(cors.New(cors.Config{
		AllowMethods:     allows.Methods,
		AllowHeaders:     allows.Headers,
		AllowOrigins:     allows.Origins,
		AllowCredentials: false,
		MaxAge:           12 * time.Hour,
	}))

	p := ginprom.New(
		ginprom.Engine(app),
		ginprom.Subsystem("gin"),
		ginprom.Path("/metrics"),
		ginprom.Ignore("/swagger/*any"),
	)
	app.Use(p.Instrument())

	db := database.DBClient()

	// -----> Routes Start
	api := app.Group("/api/v1")

	// Version routes
	version_repo := version.NewRepo(db)
	version_service := version.NewService(version_repo)
	routes.VersionRoutes(api, version_service)

	// Auth routes
	auth_repo := auth.NewRepo(db)
	auth_service := auth.NewService(auth_repo)
	routes.AuthRoutes(api, auth_service)

	// Initialize token blacklist repository
	state.SetTokenBlacklistRepository(auth_repo)

	// Category routes
	category_repo := category.NewRepo(db)
	category_service := category.NewService(category_repo)
	routes.CategoryRoutes(api, category_service)

	// Account routes
	account_repo := account.NewRepo(db)
	account_service := account.NewService(account_repo)
	routes.AccountRoutes(api, account_service)

	// Transaction routes
	transaction_repo := transaction.NewRepo(db)
	transaction_service := transaction.NewService(transaction_repo, category_service, account_service)
	routes.TransactionRoutes(api, transaction_service)

	// Bank Statement routes
	bank_statement_repo := bank_statement.NewRepo(db)
	bank_statement_service := bank_statement.NewService(bank_statement_repo, transaction_service)
	routes.BankStatementRoutes(api, bank_statement_service)

	// Report routes
	report_repo := report.NewRepo(db)
	report_service := report.NewService(report_repo)
	routes.ReportRoutes(api, report_service)

	// Recurring transaction routes
	recurring_repo := recurring.NewRepo(db)
	recurring_service := recurring.NewService(recurring_repo, category_service, account_service, transaction_service)
	routes.RecurringRoutes(api, recurring_service)

	// Routes End <-----

	// Swagger documentation
	docs.SwaggerInfo.Host = config.InitConfig().App.BaseUrl
	docs.SwaggerInfo.Version = os.Getenv("APP_VERSION")

	// Swagger UI without authentication
	app.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// Redirect /docs to Swagger UI
	app.GET("/docs", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "/swagger/index.html")
	})

	// Legacy Swagger UI with authentication
	if os.Getenv("SWAGGER_USER") != "" {
		swaggerUser = os.Getenv("SWAGGER_USER")
	} else {
		swaggerUser = "fin_notebook-dev"
	}
	if os.Getenv("SWAGGER_PASS") != "" {
		swaggerPass = os.Getenv("SWAGGER_PASS")
	} else {
		swaggerPass = "fin_notebook-dev"
	}

	app.GET("/docs/*any",
		gin.BasicAuth(gin.Accounts{
			swaggerUser: swaggerPass,
		}),
		ginSwagger.WrapHandler(swaggerFiles.Handler),
	)

	// Check if we're in development mode (hot reload)
	if os.Getenv("DEV_MODE") == "true" || gin.Mode() == gin.DebugMode {
		// Serve static files from the file system in development mode
		app.Static("/assets", "./dist/assets")
		app.Static("/_next", "./dist/_next")
		app.StaticFile("/favicon.ico", "./dist/favicon.ico")
		app.StaticFile("/robots.txt", "./dist/robots.txt")

		// Handle specific routes for SPA
		app.GET("/login", func(c *gin.Context) {
			c.File("./dist/login.html")
		})
		app.GET("/register", func(c *gin.Context) {
			c.File("./dist/register.html")
		})
		app.GET("/dashboard", func(c *gin.Context) {
			c.File("./dist/dashboard.html")
		})
		app.GET("/transactions", func(c *gin.Context) {
			c.File("./dist/transactions.html")
		})

		// Serve the main index.html for root
		app.GET("/", func(c *gin.Context) {
			c.File("./dist/index.html")
		})

		// Block .txt files - handle them in NoRoute instead

		// Fallback for unknown routes
		app.NoRoute(func(c *gin.Context) {
			// Don't serve .txt files
			if path.Ext(c.Request.URL.Path) == ".txt" {
				c.Status(404)
				return
			}
			c.File("./dist/index.html")
		})
	} else {
		// Use embedded files in production mode
		app.GET("/assets/*filepath", func(c *gin.Context) {
			c.FileFromFS(path.Join("/dist/", c.Request.URL.Path), http.FS(embed.StaticsFS()))
		})
		app.Any("/", func(c *gin.Context) {
			c.FileFromFS("dist/index.html", http.FS(embed.StaticsFS()))
		})
		app.GET("/robots.txt", func(c *gin.Context) {
			c.FileFromFS("dist/robots.txt", http.FS(embed.StaticsFS()))
		})
		app.GET("/favicon.ico", func(c *gin.Context) {
			c.FileFromFS("dist/favicon.ico", http.FS(embed.StaticsFS()))
		})
		app.NoRoute(func(c *gin.Context) {
			c.FileFromFS("dist/index.html", http.FS(embed.StaticsFS()))
		})
	}

	fmt.Println("Server is running on port " + appc.Port)
	app.Run(net.JoinHostPort(appc.Host, appc.Port))
}
