package entities

import (
	"github.com/google/uuid"
)

// BankStatementEntry represents a single transaction entry from a bank statement
type BankStatementEntry struct {
	Base
	Date        string    `json:"date"`
	Description string    `json:"description"`
	Amount      float64   `json:"amount"`
	Type        string    `json:"type"` // "expense" or "income"
	UserID      uuid.UUID `json:"user_id"`
	User        User      `json:"user" gorm:"foreignKey:UserID"`
}
