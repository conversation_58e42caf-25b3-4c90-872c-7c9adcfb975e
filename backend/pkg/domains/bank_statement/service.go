package bank_statement

import (
	"bufio"
	"fmt"
	"math"
	"mime/multipart"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/NocyTech/fin_notebook/pkg/domains/transaction"
	"github.com/NocyTech/fin_notebook/pkg/dtos"
	"github.com/NocyTech/fin_notebook/pkg/utils"
)

// Service defines the interface for bank statement operations
type Service interface {
	ParseVakifbankStatement(userID string, file multipart.File) ([]dtos.BankStatementEntry, error)
	ImportBankStatementEntries(userID string, entries []dtos.BankStatementImportRequest) ([]dtos.TransactionResponse, error)
}

// service implements the Service interface
type service struct {
	repository         Repository
	transactionService transaction.Service
}

// NewService creates a new bank statement service
func NewService(r Repository, ts transaction.Service) Service {
	return &service{
		repository:         r,
		transactionService: ts,
	}
}

// ParseVakifbankStatement parses a Vakifbank statement PDF and extracts transactions
func (s *service) ParseVakifbankStatement(userID string, file multipart.File) ([]dtos.BankStatementEntry, error) {
	// For testing purposes, let's use a hardcoded sample of transaction data
	// This will allow us to test the rest of the flow while we work on the PDF parsing
	fmt.Println("Using sample transaction data for testing")

	// Sample transaction data in the format we expect from a Vakifbank statement
	sampleData := `
TARİH SAAT İŞLEM NO MİKTAR BAKİYE İŞLEM ADI
19.04.2025 12:05 **************** 200,00 200,00 Gelen FAST Anlık Ödeme
23.04.2025 18:27 **************** 366,53 566,53 Yatırım Hesabından Virman
23.04.2025 18:28 **************** -550,53 16,00 Virman
23.04.2025 21:56 **************** 550,53 566,53 Virman
23.04.2025 21:56 **************** -550,53 16,00 FAST Anlık Ödeme
23.04.2025 21:56 **************** -6,39 9,61 Masraf Tahsilatı
25.04.2025 09:41 **************** 1.000,00 1.009,61 Gelen FAST Anlık Ödeme
25.04.2025 09:41 **************** -1.000,00 9,61 ATM QR Para Çek
13.05.2025 06:12 **************** -2,67 6,94 MKK Masrafı
14.05.2025 10:24 **************** 446,52 453,46 Gelen EFT
`

	// Parse the sample data
	return s.parseVakifbankStatementText(sampleData)
}

// parseVakifbankStatementText parses the extracted text from a Vakifbank statement
func (s *service) parseVakifbankStatementText(text string) ([]dtos.BankStatementEntry, error) {
	var entries []dtos.BankStatementEntry

	// Split the text into lines
	scanner := bufio.NewScanner(strings.NewReader(text))
	var lines []string
	for scanner.Scan() {
		line := scanner.Text()
		if strings.TrimSpace(line) != "" {
			lines = append(lines, line)
		}
	}

	// If scanner failed, try splitting by newlines directly
	if len(lines) == 0 {
		lines = strings.Split(text, "\n")
	}

	fmt.Printf("Processing %d lines of text\n", len(lines))

	// Find the header line
	headerIndex := -1
	for i, line := range lines {
		if (strings.Contains(line, "TARİH") || strings.Contains(line, "TARIH")) &&
			(strings.Contains(line, "SAAT") || strings.Contains(line, "ISLEM")) &&
			(strings.Contains(line, "MİKTAR") || strings.Contains(line, "MIKTAR")) {
			headerIndex = i
			fmt.Printf("Found header line at index: %d Content: %s\n", i, line)
			break
		}
	}

	// If we can't find a header, try to parse all lines with dates
	if headerIndex == -1 {
		fmt.Println("Could not find transaction header, trying to parse all lines with dates")

		// Try different patterns for transaction lines
		// Pattern 1: Date at the beginning of the line
		dateRegex1 := regexp.MustCompile(`^(\d{2}\.\d{2}\.\d{4})`)

		// Pattern 2: Date anywhere in the line
		dateRegex2 := regexp.MustCompile(`(\d{2}\.\d{2}\.\d{4})`)

		// Pattern 3: Amount pattern (numbers with comma or dot)
		amountRegex := regexp.MustCompile(`([+-]?[\d,.]+)`)

		for _, line := range lines {
			line = strings.TrimSpace(line)
			if line == "" {
				continue
			}

			var date, description string
			var amount float64

			// Try to find a date
			dateMatch := dateRegex1.FindStringSubmatch(line)
			if len(dateMatch) < 2 {
				dateMatch = dateRegex2.FindStringSubmatch(line)
			}

			if len(dateMatch) >= 2 {
				date = dateMatch[1]

				// Try to find an amount
				amountMatches := amountRegex.FindAllStringSubmatch(line, -1)
				if len(amountMatches) >= 2 { // We need at least 2 numbers (one might be a date)
					// Try each potential amount
					for _, amountMatch := range amountMatches {
						if len(amountMatch) >= 2 {
							amountStr := amountMatch[1]
							// Skip if it looks like a date
							if strings.Contains(amountStr, ".") && len(amountStr) >= 8 {
								continue
							}

							// Clean and parse the amount
							amountStr = strings.ReplaceAll(amountStr, ".", "")
							amountStr = strings.ReplaceAll(amountStr, ",", ".")
							amountFloat, parseErr := strconv.ParseFloat(amountStr, 64)
							if parseErr == nil && amountFloat != 0 {
								amount = amountFloat
								break
							}
						}
					}

					// Extract description (everything that's not date or amount)
					description = line
					description = dateRegex1.ReplaceAllString(description, "")
					description = dateRegex2.ReplaceAllString(description, "")
					description = amountRegex.ReplaceAllString(description, "")
					description = strings.TrimSpace(description)

					if date != "" && amount != 0 {
						// Determine transaction type
						transactionType := "expense"
						if amount > 0 {
							transactionType = "income"
						}

						// Auto-assign category based on description and type
						categoryID := utils.GetCategoryIDForTransaction(description, transactionType)

						// Auto-assign account based on description
						accountID := utils.GetAccountIDForTransaction(description)

						// Create entry
						entry := dtos.BankStatementEntry{
							Date:        date,
							Description: description,
							Amount:      math.Abs(amount),
							Type:        transactionType,
							CategoryID:  categoryID,
							AccountID:   accountID,
						}

						entries = append(entries, entry)
					}
				}
			}
		}
	} else {
		// Process transaction lines with the header
		// Regular expression to match date, amount, and description
		dateRegex := regexp.MustCompile(`(\d{2}\.\d{2}\.\d{4})\s+(\d{2}:\d{2})\s+(\d+)\s+([+-]?[\d,.]+)\s+([+-]?[\d,.]+)\s+(.+)`)

		// Simpler regex as fallback
		simpleDateRegex := regexp.MustCompile(`(\d{2}\.\d{2}\.\d{4}).*?([+-]?[\d,.]+).*?(.+)`)

		for i := headerIndex + 1; i < len(lines); i++ {
			line := lines[i]

			// Skip empty lines
			if strings.TrimSpace(line) == "" {
				continue
			}

			// Try to match the line with our regex
			matches := dateRegex.FindStringSubmatch(line)
			if len(matches) >= 7 {
				date := matches[1]
				amount := matches[4]
				description := matches[6]

				// Clean and parse the amount
				amount = strings.ReplaceAll(amount, ".", "")
				amount = strings.ReplaceAll(amount, ",", ".")
				amountFloat, err := strconv.ParseFloat(amount, 64)
				if err != nil {
					continue
				}

				// Determine transaction type
				transactionType := "expense"
				if amountFloat > 0 {
					transactionType = "income"
				}

				// Auto-assign category based on description and type
				categoryID := utils.GetCategoryIDForTransaction(description, transactionType)

				// Auto-assign account based on description
				accountID := utils.GetAccountIDForTransaction(description)

				// Create entry
				entry := dtos.BankStatementEntry{
					Date:        date,
					Description: description,
					Amount:      math.Abs(amountFloat),
					Type:        transactionType,
					CategoryID:  categoryID,
					AccountID:   accountID,
				}

				entries = append(entries, entry)
			} else {
				// Try with simpler regex as fallback
				matches = simpleDateRegex.FindStringSubmatch(line)
				if len(matches) >= 4 {
					date := matches[1]
					amount := matches[2]
					description := matches[3]

					// Clean and parse the amount
					amount = strings.ReplaceAll(amount, ".", "")
					amount = strings.ReplaceAll(amount, ",", ".")
					amountFloat, err := strconv.ParseFloat(amount, 64)
					if err != nil {
						continue
					}

					// Determine transaction type
					transactionType := "expense"
					if amountFloat > 0 {
						transactionType = "income"
					}

					// Auto-assign category based on description and type
					categoryID := utils.GetCategoryIDForTransaction(description, transactionType)

					// Auto-assign account based on description
					accountID := utils.GetAccountIDForTransaction(description)

					// Create entry
					entry := dtos.BankStatementEntry{
						Date:        date,
						Description: description,
						Amount:      math.Abs(amountFloat),
						Type:        transactionType,
						CategoryID:  categoryID,
						AccountID:   accountID,
					}

					entries = append(entries, entry)
				}
			}
		}
	}

	fmt.Printf("Found %d transaction entries\n", len(entries))
	return entries, nil
}

// ImportBankStatementEntries imports bank statement entries as transactions
func (s *service) ImportBankStatementEntries(userID string, entries []dtos.BankStatementImportRequest) ([]dtos.TransactionResponse, error) {
	var responses []dtos.TransactionResponse

	for _, entry := range entries {
		// Convert date format if needed
		transactionDate, err := time.Parse("02.01.2006", entry.Date)
		if err != nil {
			// Try alternative format
			transactionDate, err = time.Parse("2006-01-02", entry.Date)
			if err != nil {
				return nil, fmt.Errorf("invalid date format: %s", entry.Date)
			}
		}

		// Auto-assign category if not provided or empty
		categoryID := entry.CategoryID
		if categoryID == "" {
			categoryID = utils.GetCategoryIDForTransaction(entry.Description, entry.Type)
		}

		// Auto-assign account if not provided or empty
		accountID := entry.AccountID
		if accountID == "" {
			accountID = utils.GetAccountIDForTransaction(entry.Description)
		}

		// Create transaction request
		transactionReq := &dtos.TransactionRequest{
			Title:           entry.Description,
			Type:            entry.Type,
			Amount:          entry.Amount,
			Currency:        "TRY",           // Default to Turkish Lira
			CategoryID:      categoryID,      // Auto-assigned if needed
			PaymentMethod:   "bank_transfer", // Default payment method
			AccountID:       accountID,       // Auto-assigned if needed
			TransactionDate: transactionDate,
		}

		// Create transaction
		response, err := s.transactionService.CreateTransaction(userID, transactionReq)
		if err != nil {
			return nil, err
		}

		responses = append(responses, *response)
	}

	return responses, nil
}
