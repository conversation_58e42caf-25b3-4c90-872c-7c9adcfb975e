package routes

import (
	"github.com/NocyTech/fin_notebook/pkg/domains/auth"
	"github.com/NocyTech/fin_notebook/pkg/dtos"
	"github.com/NocyTech/fin_notebook/pkg/entities"
	"github.com/NocyTech/fin_notebook/pkg/fin_notebooklog"
	"github.com/NocyTech/fin_notebook/pkg/middleware"
	"github.com/NocyTech/fin_notebook/pkg/state"
	"github.com/gin-gonic/gin"
)

func AuthRoutes(r *gin.RouterGroup, s auth.Service) {
	r.POST("/login", Login(s))
	r.POST("/register", Register(s))
	r.GET("/me", middleware.Authorized(), GetMe(s))
	r.POST("/logout", middleware.Authorized(), Logout(s))
}

// @Summary Login user
// @Description Login with username and password to get JWT token
// @Tags auth
// @Accept json
// @Produce json
// @Param request body dtos.AuthenticationRequest true "Login credentials"
// @Success 200 {object} map[string]interface{} "Returns JWT token and user data"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Router /login [post]
func Login(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.AuthenticationRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			fin_notebooklog.CreateLog(&entities.Log{
				Title:   "Login Failed",
				Message: "Login Failed, bind json err:" + err.Error(),
				Entity:  "user",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		resp, err := s.Login(&req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Register new user
// @Description Register a new user with username, email, password and name
// @Tags auth
// @Accept json
// @Produce json
// @Param request body dtos.RegisterRequest true "User registration data"
// @Success 201 {object} map[string]interface{} "Returns JWT token and user data"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Router /register [post]
func Register(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RegisterRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			fin_notebooklog.CreateLog(&entities.Log{
				Title:   "Register Failed",
				Message: "Register Failed, bind json err:" + err.Error(),
				Entity:  "user",
				Type:    "error",
				Ip:      c.ClientIP(),
			})

			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.Register(&req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		c.JSON(201, gin.H{
			"data":   resp,
			"status": 201,
		})
	}
}

// @Summary Get current user
// @Description Get information about the currently logged in user
// @Tags auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{} "Returns user data"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Router /auth/me [get]
func GetMe(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		// Get user ID from context (set by middleware.Authorized())
		userID := state.GetCurrentUserID(c)

		// Get user by ID
		user, err := s.GetUserByID(userID.String())
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   user,
			"status": 200,
		})
	}
}

// @Summary Logout user
// @Description Logout the current user by invalidating the JWT token
// @Tags auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{} "Success message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Router /logout [post]
func Logout(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		// Get user ID from context
		userID := state.GetCurrentUserID(c)

		// Get token from context
		token := state.GetCurrentToken(c)
		if token == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "token not found",
				"status": 400,
			})
			return
		}

		// Logout the user
		err := s.Logout(token, userID.String())
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   "logout successful",
			"status": 200,
		})
	}
}
