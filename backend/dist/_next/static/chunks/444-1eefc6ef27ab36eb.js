"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[444],{5196:(e,t,r)=>{r.d(t,{A:()=>l});let l=(0,r(19946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},13717:(e,t,r)=>{r.d(t,{A:()=>l});let l=(0,r(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},39881:(e,t,r)=>{r.d(t,{A:()=>l});let l=(0,r(19946).A)("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]])},47863:(e,t,r)=>{r.d(t,{A:()=>l});let l=(0,r(19946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},47924:(e,t,r)=>{r.d(t,{A:()=>l});let l=(0,r(19946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},58832:(e,t,r)=>{r.d(t,{A:()=>l});let l=(0,r(19946).A)("arrow-down",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]])},62525:(e,t,r)=>{r.d(t,{A:()=>l});let l=(0,r(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},66474:(e,t,r)=>{r.d(t,{A:()=>l});let l=(0,r(19946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},78893:(e,t,r)=>{r.d(t,{UC:()=>eI,In:()=>eR,q7:()=>eE,VF:()=>eL,p4:()=>eD,ZL:()=>eA,bL:()=>eT,wn:()=>e_,PP:()=>eH,l9:()=>eN,WT:()=>eM,LM:()=>eP});var l=r(12115),n=r(47650);function o(e,[t,r]){return Math.min(r,Math.max(t,e))}var a=r(85185),i=r(37328),s=r(6101),d=r(46081),u=r(94315),c=r(19178),p=r(92293),h=r(25519),f=r(61285),v=r(38795),m=r(34378),w=r(63655),g=r(99708),y=r(39033),x=r(5845),b=r(52712),S=r(95155),C=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});l.forwardRef((e,t)=>(0,S.jsx)(w.sG.span,{...e,ref:t,style:{...C,...e.style}})).displayName="VisuallyHidden";var k=r(38168),j=r(93795),T=[" ","Enter","ArrowUp","ArrowDown"],N=[" ","Enter"],M="Select",[R,A,I]=(0,i.N)(M),[P,E]=(0,d.A)(M,[I,v.Bk]),D=(0,v.Bk)(),[L,H]=P(M),[_,B]=P(M),V=e=>{let{__scopeSelect:t,children:r,open:n,defaultOpen:o,onOpenChange:a,value:i,defaultValue:s,onValueChange:d,dir:c,name:p,autoComplete:h,disabled:m,required:w,form:g}=e,y=D(t),[b,C]=l.useState(null),[k,j]=l.useState(null),[T,N]=l.useState(!1),A=(0,u.jH)(c),[I,P]=(0,x.i)({prop:n,defaultProp:null!=o&&o,onChange:a,caller:M}),[E,H]=(0,x.i)({prop:i,defaultProp:s,onChange:d,caller:M}),B=l.useRef(null),V=!b||g||!!b.closest("form"),[G,O]=l.useState(new Set),F=Array.from(G).map(e=>e.props.value).join(";");return(0,S.jsx)(v.bL,{...y,children:(0,S.jsxs)(L,{required:w,scope:t,trigger:b,onTriggerChange:C,valueNode:k,onValueNodeChange:j,valueNodeHasChildren:T,onValueNodeHasChildrenChange:N,contentId:(0,f.B)(),value:E,onValueChange:H,open:I,onOpenChange:P,dir:A,triggerPointerDownPosRef:B,disabled:m,children:[(0,S.jsx)(R.Provider,{scope:t,children:(0,S.jsx)(_,{scope:e.__scopeSelect,onNativeOptionAdd:l.useCallback(e=>{O(t=>new Set(t).add(e))},[]),onNativeOptionRemove:l.useCallback(e=>{O(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),V?(0,S.jsxs)(eS,{"aria-hidden":!0,required:w,tabIndex:-1,name:p,autoComplete:h,value:E,onChange:e=>H(e.target.value),disabled:m,form:g,children:[void 0===E?(0,S.jsx)("option",{value:""}):null,Array.from(G)]},F):null]})})};V.displayName=M;var G="SelectTrigger",O=l.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:n=!1,...o}=e,i=D(r),d=H(G,r),u=d.disabled||n,c=(0,s.s)(t,d.onTriggerChange),p=A(r),h=l.useRef("touch"),[f,m,g]=ek(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===d.value),l=ej(t,e,r);void 0!==l&&d.onValueChange(l.value)}),y=e=>{u||(d.onOpenChange(!0),g()),e&&(d.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,S.jsx)(v.Mz,{asChild:!0,...i,children:(0,S.jsx)(w.sG.button,{type:"button",role:"combobox","aria-controls":d.contentId,"aria-expanded":d.open,"aria-required":d.required,"aria-autocomplete":"none",dir:d.dir,"data-state":d.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":eC(d.value)?"":void 0,...o,ref:c,onClick:(0,a.m)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==h.current&&y(e)}),onPointerDown:(0,a.m)(o.onPointerDown,e=>{h.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:(0,a.m)(o.onKeyDown,e=>{let t=""!==f.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&T.includes(e.key)&&(y(),e.preventDefault())})})})});O.displayName=G;var F="SelectValue",K=l.forwardRef((e,t)=>{let{__scopeSelect:r,className:l,style:n,children:o,placeholder:a="",...i}=e,d=H(F,r),{onValueNodeHasChildrenChange:u}=d,c=void 0!==o,p=(0,s.s)(t,d.onValueNodeChange);return(0,b.N)(()=>{u(c)},[u,c]),(0,S.jsx)(w.sG.span,{...i,ref:p,style:{pointerEvents:"none"},children:eC(d.value)?(0,S.jsx)(S.Fragment,{children:a}):o})});K.displayName=F;var W=l.forwardRef((e,t)=>{let{__scopeSelect:r,children:l,...n}=e;return(0,S.jsx)(w.sG.span,{"aria-hidden":!0,...n,ref:t,children:l||"▼"})});W.displayName="SelectIcon";var q=e=>(0,S.jsx)(m.Z,{asChild:!0,...e});q.displayName="SelectPortal";var z="SelectContent",U=l.forwardRef((e,t)=>{let r=H(z,e.__scopeSelect),[o,a]=l.useState();return((0,b.N)(()=>{a(new DocumentFragment)},[]),r.open)?(0,S.jsx)(J,{...e,ref:t}):o?n.createPortal((0,S.jsx)(Z,{scope:e.__scopeSelect,children:(0,S.jsx)(R.Slot,{scope:e.__scopeSelect,children:(0,S.jsx)("div",{children:e.children})})}),o):null});U.displayName=z;var[Z,X]=P(z),Y=(0,g.TL)("SelectContent.RemoveScroll"),J=l.forwardRef((e,t)=>{let{__scopeSelect:r,position:n="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:d,side:u,sideOffset:f,align:v,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:y,sticky:x,hideWhenDetached:b,avoidCollisions:C,...T}=e,N=H(z,r),[M,R]=l.useState(null),[I,P]=l.useState(null),E=(0,s.s)(t,e=>R(e)),[D,L]=l.useState(null),[_,B]=l.useState(null),V=A(r),[G,O]=l.useState(!1),F=l.useRef(!1);l.useEffect(()=>{if(M)return(0,k.Eq)(M)},[M]),(0,p.Oh)();let K=l.useCallback(e=>{let[t,...r]=V().map(e=>e.ref.current),[l]=r.slice(-1),n=document.activeElement;for(let r of e)if(r===n||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&I&&(I.scrollTop=0),r===l&&I&&(I.scrollTop=I.scrollHeight),null==r||r.focus(),document.activeElement!==n))return},[V,I]),W=l.useCallback(()=>K([D,M]),[K,D,M]);l.useEffect(()=>{G&&W()},[G,W]);let{onOpenChange:q,triggerPointerDownPosRef:U}=N;l.useEffect(()=>{if(M){let e={x:0,y:0},t=t=>{var r,l,n,o;e={x:Math.abs(Math.round(t.pageX)-(null!=(n=null==(r=U.current)?void 0:r.x)?n:0)),y:Math.abs(Math.round(t.pageY)-(null!=(o=null==(l=U.current)?void 0:l.y)?o:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():M.contains(r.target)||q(!1),document.removeEventListener("pointermove",t),U.current=null};return null!==U.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[M,q,U]),l.useEffect(()=>{let e=()=>q(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[q]);let[X,J]=ek(e=>{let t=V().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),l=ej(t,e,r);l&&setTimeout(()=>l.ref.current.focus())}),ee=l.useCallback((e,t,r)=>{let l=!F.current&&!r;(void 0!==N.value&&N.value===t||l)&&(L(e),l&&(F.current=!0))},[N.value]),et=l.useCallback(()=>null==M?void 0:M.focus(),[M]),er=l.useCallback((e,t,r)=>{let l=!F.current&&!r;(void 0!==N.value&&N.value===t||l)&&B(e)},[N.value]),el="popper"===n?$:Q,en=el===$?{side:u,sideOffset:f,align:v,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:y,sticky:x,hideWhenDetached:b,avoidCollisions:C}:{};return(0,S.jsx)(Z,{scope:r,content:M,viewport:I,onViewportChange:P,itemRefCallback:ee,selectedItem:D,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:W,selectedItemText:_,position:n,isPositioned:G,searchRef:X,children:(0,S.jsx)(j.A,{as:Y,allowPinchZoom:!0,children:(0,S.jsx)(h.n,{asChild:!0,trapped:N.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(o,e=>{var t;null==(t=N.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,S.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:d,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>N.onOpenChange(!1),children:(0,S.jsx)(el,{role:"listbox",id:N.contentId,"data-state":N.open?"open":"closed",dir:N.dir,onContextMenu:e=>e.preventDefault(),...T,...en,onPlaced:()=>O(!0),ref:E,style:{display:"flex",flexDirection:"column",outline:"none",...T.style},onKeyDown:(0,a.m)(T.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||J(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=V().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,l=t.indexOf(r);t=t.slice(l+1)}setTimeout(()=>K(t)),e.preventDefault()}})})})})})})});J.displayName="SelectContentImpl";var Q=l.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:n,...a}=e,i=H(z,r),d=X(z,r),[u,c]=l.useState(null),[p,h]=l.useState(null),f=(0,s.s)(t,e=>h(e)),v=A(r),m=l.useRef(!1),g=l.useRef(!0),{viewport:y,selectedItem:x,selectedItemText:C,focusSelectedItem:k}=d,j=l.useCallback(()=>{if(i.trigger&&i.valueNode&&u&&p&&y&&x&&C){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),l=C.getBoundingClientRect();if("rtl"!==i.dir){let n=l.left-t.left,a=r.left-n,i=e.left-a,s=e.width+i,d=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-d)]);u.style.minWidth=s+"px",u.style.left=c+"px"}else{let n=t.right-l.right,a=window.innerWidth-r.right-n,i=window.innerWidth-e.right-a,s=e.width+i,d=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-d)]);u.style.minWidth=s+"px",u.style.right=c+"px"}let a=v(),s=window.innerHeight-20,d=y.scrollHeight,c=window.getComputedStyle(p),h=parseInt(c.borderTopWidth,10),f=parseInt(c.paddingTop,10),w=parseInt(c.borderBottomWidth,10),g=h+f+d+parseInt(c.paddingBottom,10)+w,b=Math.min(5*x.offsetHeight,g),S=window.getComputedStyle(y),k=parseInt(S.paddingTop,10),j=parseInt(S.paddingBottom,10),T=e.top+e.height/2-10,N=x.offsetHeight/2,M=h+f+(x.offsetTop+N);if(M<=T){let e=a.length>0&&x===a[a.length-1].ref.current;u.style.bottom="0px";let t=Math.max(s-T,N+(e?j:0)+(p.clientHeight-y.offsetTop-y.offsetHeight)+w);u.style.height=M+t+"px"}else{let e=a.length>0&&x===a[0].ref.current;u.style.top="0px";let t=Math.max(T,h+y.offsetTop+(e?k:0)+N);u.style.height=t+(g-M)+"px",y.scrollTop=M-T+y.offsetTop}u.style.margin="".concat(10,"px 0"),u.style.minHeight=b+"px",u.style.maxHeight=s+"px",null==n||n(),requestAnimationFrame(()=>m.current=!0)}},[v,i.trigger,i.valueNode,u,p,y,x,C,i.dir,n]);(0,b.N)(()=>j(),[j]);let[T,N]=l.useState();(0,b.N)(()=>{p&&N(window.getComputedStyle(p).zIndex)},[p]);let M=l.useCallback(e=>{e&&!0===g.current&&(j(),null==k||k(),g.current=!1)},[j,k]);return(0,S.jsx)(ee,{scope:r,contentWrapper:u,shouldExpandOnScrollRef:m,onScrollButtonChange:M,children:(0,S.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:T},children:(0,S.jsx)(w.sG.div,{...a,ref:f,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});Q.displayName="SelectItemAlignedPosition";var $=l.forwardRef((e,t)=>{let{__scopeSelect:r,align:l="start",collisionPadding:n=10,...o}=e,a=D(r);return(0,S.jsx)(v.UC,{...a,...o,ref:t,align:l,collisionPadding:n,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});$.displayName="SelectPopperPosition";var[ee,et]=P(z,{}),er="SelectViewport",el=l.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:n,...o}=e,i=X(er,r),d=et(er,r),u=(0,s.s)(t,i.onViewportChange),c=l.useRef(0);return(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:n}),(0,S.jsx)(R.Slot,{scope:r,children:(0,S.jsx)(w.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,a.m)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:l}=d;if((null==l?void 0:l.current)&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let l=window.innerHeight-20,n=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(n<l){let o=n+e,a=Math.min(l,o),i=o-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});el.displayName=er;var en="SelectGroup",[eo,ea]=P(en);l.forwardRef((e,t)=>{let{__scopeSelect:r,...l}=e,n=(0,f.B)();return(0,S.jsx)(eo,{scope:r,id:n,children:(0,S.jsx)(w.sG.div,{role:"group","aria-labelledby":n,...l,ref:t})})}).displayName=en;var ei="SelectLabel";l.forwardRef((e,t)=>{let{__scopeSelect:r,...l}=e,n=ea(ei,r);return(0,S.jsx)(w.sG.div,{id:n.id,...l,ref:t})}).displayName=ei;var es="SelectItem",[ed,eu]=P(es),ec=l.forwardRef((e,t)=>{let{__scopeSelect:r,value:n,disabled:o=!1,textValue:i,...d}=e,u=H(es,r),c=X(es,r),p=u.value===n,[h,v]=l.useState(null!=i?i:""),[m,g]=l.useState(!1),y=(0,s.s)(t,e=>{var t;return null==(t=c.itemRefCallback)?void 0:t.call(c,e,n,o)}),x=(0,f.B)(),b=l.useRef("touch"),C=()=>{o||(u.onValueChange(n),u.onOpenChange(!1))};if(""===n)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,S.jsx)(ed,{scope:r,value:n,disabled:o,textId:x,isSelected:p,onItemTextChange:l.useCallback(e=>{v(t=>{var r;return t||(null!=(r=null==e?void 0:e.textContent)?r:"").trim()})},[]),children:(0,S.jsx)(R.ItemSlot,{scope:r,value:n,disabled:o,textValue:h,children:(0,S.jsx)(w.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":m?"":void 0,"aria-selected":p&&m,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...d,ref:y,onFocus:(0,a.m)(d.onFocus,()=>g(!0)),onBlur:(0,a.m)(d.onBlur,()=>g(!1)),onClick:(0,a.m)(d.onClick,()=>{"mouse"!==b.current&&C()}),onPointerUp:(0,a.m)(d.onPointerUp,()=>{"mouse"===b.current&&C()}),onPointerDown:(0,a.m)(d.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,a.m)(d.onPointerMove,e=>{if(b.current=e.pointerType,o){var t;null==(t=c.onItemLeave)||t.call(c)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(d.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=c.onItemLeave)||t.call(c)}}),onKeyDown:(0,a.m)(d.onKeyDown,e=>{var t;((null==(t=c.searchRef)?void 0:t.current)===""||" "!==e.key)&&(N.includes(e.key)&&C()," "===e.key&&e.preventDefault())})})})})});ec.displayName=es;var ep="SelectItemText",eh=l.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:a,...i}=e,d=H(ep,r),u=X(ep,r),c=eu(ep,r),p=B(ep,r),[h,f]=l.useState(null),v=(0,s.s)(t,e=>f(e),c.onItemTextChange,e=>{var t;return null==(t=u.itemTextRefCallback)?void 0:t.call(u,e,c.value,c.disabled)}),m=null==h?void 0:h.textContent,g=l.useMemo(()=>(0,S.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:y,onNativeOptionRemove:x}=p;return(0,b.N)(()=>(y(g),()=>x(g)),[y,x,g]),(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)(w.sG.span,{id:c.textId,...i,ref:v}),c.isSelected&&d.valueNode&&!d.valueNodeHasChildren?n.createPortal(i.children,d.valueNode):null]})});eh.displayName=ep;var ef="SelectItemIndicator",ev=l.forwardRef((e,t)=>{let{__scopeSelect:r,...l}=e;return eu(ef,r).isSelected?(0,S.jsx)(w.sG.span,{"aria-hidden":!0,...l,ref:t}):null});ev.displayName=ef;var em="SelectScrollUpButton",ew=l.forwardRef((e,t)=>{let r=X(em,e.__scopeSelect),n=et(em,e.__scopeSelect),[o,a]=l.useState(!1),i=(0,s.s)(t,n.onScrollButtonChange);return(0,b.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,S.jsx)(ex,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ew.displayName=em;var eg="SelectScrollDownButton",ey=l.forwardRef((e,t)=>{let r=X(eg,e.__scopeSelect),n=et(eg,e.__scopeSelect),[o,a]=l.useState(!1),i=(0,s.s)(t,n.onScrollButtonChange);return(0,b.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,S.jsx)(ex,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});ey.displayName=eg;var ex=l.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:n,...o}=e,i=X("SelectScrollButton",r),s=l.useRef(null),d=A(r),u=l.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return l.useEffect(()=>()=>u(),[u]),(0,b.N)(()=>{var e;let t=d().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[d]),(0,S.jsx)(w.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.m)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(n,50))}),onPointerMove:(0,a.m)(o.onPointerMove,()=>{var e;null==(e=i.onItemLeave)||e.call(i),null===s.current&&(s.current=window.setInterval(n,50))}),onPointerLeave:(0,a.m)(o.onPointerLeave,()=>{u()})})});l.forwardRef((e,t)=>{let{__scopeSelect:r,...l}=e;return(0,S.jsx)(w.sG.div,{"aria-hidden":!0,...l,ref:t})}).displayName="SelectSeparator";var eb="SelectArrow";l.forwardRef((e,t)=>{let{__scopeSelect:r,...l}=e,n=D(r),o=H(eb,r),a=X(eb,r);return o.open&&"popper"===a.position?(0,S.jsx)(v.i3,{...n,...l,ref:t}):null}).displayName=eb;var eS=l.forwardRef((e,t)=>{let{__scopeSelect:r,value:n,...o}=e,a=l.useRef(null),i=(0,s.s)(t,a),d=function(e){let t=l.useRef({value:e,previous:e});return l.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(n);return l.useEffect(()=>{let e=a.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(d!==n&&t){let r=new Event("change",{bubbles:!0});t.call(e,n),e.dispatchEvent(r)}},[d,n]),(0,S.jsx)(w.sG.select,{...o,style:{...C,...o.style},ref:i,defaultValue:n})});function eC(e){return""===e||void 0===e}function ek(e){let t=(0,y.c)(e),r=l.useRef(""),n=l.useRef(0),o=l.useCallback(e=>{let l=r.current+e;t(l),function e(t){r.current=t,window.clearTimeout(n.current),""!==t&&(n.current=window.setTimeout(()=>e(""),1e3))}(l)},[t]),a=l.useCallback(()=>{r.current="",window.clearTimeout(n.current)},[]);return l.useEffect(()=>()=>window.clearTimeout(n.current),[]),[r,o,a]}function ej(e,t,r){var l,n;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,i=(l=e,n=Math.max(a,0),l.map((e,t)=>l[(n+t)%l.length]));1===o.length&&(i=i.filter(e=>e!==r));let s=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return s!==r?s:void 0}eS.displayName="SelectBubbleInput";var eT=V,eN=O,eM=K,eR=W,eA=q,eI=U,eP=el,eE=ec,eD=eh,eL=ev,eH=ew,e_=ey},84616:(e,t,r)=>{r.d(t,{A:()=>l});let l=(0,r(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}}]);