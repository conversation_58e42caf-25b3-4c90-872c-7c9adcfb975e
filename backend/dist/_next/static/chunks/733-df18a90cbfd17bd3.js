"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[733],{6101:(e,r,t)=>{t.d(r,{s:()=>i,t:()=>l});var n=t(12115);function o(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function l(...e){return r=>{let t=!1,n=e.map(e=>{let n=o(e,r);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let r=0;r<n.length;r++){let t=n[r];"function"==typeof t?t():o(e[r],null)}}}}function i(...e){return n.useCallback(l(...e),e)}},19946:(e,r,t)=>{t.d(r,{A:()=>f});var n=t(12115);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),i=e=>{let r=l(e);return r.charAt(0).toUpperCase()+r.slice(1)},a=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()},u=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,n.forwardRef)((e,r)=>{let{color:t="currentColor",size:o=24,strokeWidth:l=2,absoluteStrokeWidth:i,className:c="",children:f,iconNode:d,...p}=e;return(0,n.createElement)("svg",{ref:r,...s,width:o,height:o,stroke:t,strokeWidth:i?24*Number(l)/Number(o):l,className:a("lucide",c),...!f&&!u(p)&&{"aria-hidden":"true"},...p},[...d.map(e=>{let[r,t]=e;return(0,n.createElement)(r,t)}),...Array.isArray(f)?f:[f]])}),f=(e,r)=>{let t=(0,n.forwardRef)((t,l)=>{let{className:u,...s}=t;return(0,n.createElement)(c,{ref:l,iconNode:r,className:a("lucide-".concat(o(i(e))),"lucide-".concat(e),u),...s})});return t.displayName=i(e),t}},63655:(e,r,t)=>{t.d(r,{hO:()=>u,sG:()=>a});var n=t(12115),o=t(47650),l=t(99708),i=t(95155),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,l.TL)(`Primitive.${r}`),o=n.forwardRef((e,n)=>{let{asChild:o,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(o?t:r,{...l,ref:n})});return o.displayName=`Primitive.${r}`,{...e,[r]:o}},{});function u(e,r){e&&o.flushSync(()=>e.dispatchEvent(r))}},89196:(e,r,t)=>{t.d(r,{RG:()=>R,bL:()=>S,q7:()=>D});var n=t(12115),o=t(85185),l=t(37328),i=t(6101),a=t(46081),u=t(61285),s=t(63655),c=t(39033),f=t(5845),d=t(94315),p=t(95155),m="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[w,g,y]=(0,l.N)(h),[b,R]=(0,a.A)(h,[y]),[x,A]=b(h),E=n.forwardRef((e,r)=>(0,p.jsx)(w.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(w.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(C,{...e,ref:r})})}));E.displayName=h;var C=n.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,orientation:l,loop:a=!1,dir:u,currentTabStopId:w,defaultCurrentTabStopId:y,onCurrentTabStopIdChange:b,onEntryFocus:R,preventScrollOnEntryFocus:A=!1,...E}=e,C=n.useRef(null),k=(0,i.s)(r,C),j=(0,d.jH)(u),[F,S]=(0,f.i)({prop:w,defaultProp:null!=y?y:null,onChange:b,caller:h}),[D,N]=n.useState(!1),L=(0,c.c)(R),T=g(t),_=n.useRef(!1),[G,P]=n.useState(0);return n.useEffect(()=>{let e=C.current;if(e)return e.addEventListener(m,L),()=>e.removeEventListener(m,L)},[L]),(0,p.jsx)(x,{scope:t,orientation:l,dir:j,loop:a,currentTabStopId:F,onItemFocus:n.useCallback(e=>S(e),[S]),onItemShiftTab:n.useCallback(()=>N(!0),[]),onFocusableItemAdd:n.useCallback(()=>P(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>P(e=>e-1),[]),children:(0,p.jsx)(s.sG.div,{tabIndex:D||0===G?-1:0,"data-orientation":l,...E,ref:k,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{_.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let r=!_.current;if(e.target===e.currentTarget&&r&&!D){let r=new CustomEvent(m,v);if(e.currentTarget.dispatchEvent(r),!r.defaultPrevented){let e=T().filter(e=>e.focusable);I([e.find(e=>e.active),e.find(e=>e.id===F),...e].filter(Boolean).map(e=>e.ref.current),A)}}_.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>N(!1))})})}),k="RovingFocusGroupItem",j=n.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,focusable:l=!0,active:i=!1,tabStopId:a,children:c,...f}=e,d=(0,u.B)(),m=a||d,v=A(k,t),h=v.currentTabStopId===m,y=g(t),{onFocusableItemAdd:b,onFocusableItemRemove:R,currentTabStopId:x}=v;return n.useEffect(()=>{if(l)return b(),()=>R()},[l,b,R]),(0,p.jsx)(w.ItemSlot,{scope:t,id:m,focusable:l,active:i,children:(0,p.jsx)(s.sG.span,{tabIndex:h?0:-1,"data-orientation":v.orientation,...f,ref:r,onMouseDown:(0,o.m)(e.onMouseDown,e=>{l?v.onItemFocus(m):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>v.onItemFocus(m)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void v.onItemShiftTab();if(e.target!==e.currentTarget)return;let r=function(e,r,t){var n;let o=(n=e.key,"rtl"!==t?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===r&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===r&&["ArrowUp","ArrowDown"].includes(o)))return F[o]}(e,v.orientation,v.dir);if(void 0!==r){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let t=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===r)t.reverse();else if("prev"===r||"next"===r){"prev"===r&&t.reverse();let n=t.indexOf(e.currentTarget);t=v.loop?function(e,r){return e.map((t,n)=>e[(r+n)%e.length])}(t,n+1):t.slice(n+1)}setTimeout(()=>I(t))}}),children:"function"==typeof c?c({isCurrentTabStop:h,hasTabStop:null!=x}):c})})});j.displayName=k;var F={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function I(e){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=document.activeElement;for(let n of e)if(n===t||(n.focus({preventScroll:r}),document.activeElement!==t))return}var S=E,D=j},99708:(e,r,t)=>{t.d(r,{DX:()=>a,TL:()=>i});var n=t(12115),o=t(6101),l=t(95155);function i(e){let r=function(e){let r=n.forwardRef((e,r)=>{let{children:t,...l}=e;if(n.isValidElement(t)){var i;let e,a,u=(i=t,(a=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(a=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),s=function(e,r){let t={...r};for(let n in r){let o=e[n],l=r[n];/^on[A-Z]/.test(n)?o&&l?t[n]=(...e)=>{let r=l(...e);return o(...e),r}:o&&(t[n]=o):"style"===n?t[n]={...o,...l}:"className"===n&&(t[n]=[o,l].filter(Boolean).join(" "))}return{...e,...t}}(l,t.props);return t.type!==n.Fragment&&(s.ref=r?(0,o.t)(r,u):u),n.cloneElement(t,s)}return n.Children.count(t)>1?n.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=n.forwardRef((e,t)=>{let{children:o,...i}=e,a=n.Children.toArray(o),u=a.find(s);if(u){let e=u.props.children,o=a.map(r=>r!==u?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,l.jsx)(r,{...i,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,l.jsx)(r,{...i,ref:t,children:o})});return t.displayName=`${e}.Slot`,t}var a=i("Slot"),u=Symbol("radix.slottable");function s(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===u}}}]);