"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[511],{25731:(e,t,a)=>{a.d(t,{A:()=>o});let r=a(23464).A.create({baseURL:"http://localhost:8008/api/v1",headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{{let t=localStorage.getItem("auth_token");t&&e.headers&&(e.headers.Authorization="Bearer ".concat(t))}return e},e=>Promise.reject(e)),r.interceptors.response.use(e=>(console.log("API Response:",e),e.data&&e.data.data)?e.data.data:e.data?e.data:(console.warn("Empty response data"),{}),e=>{var t,a,r;return console.error("API Error:",e),(null==(t=e.response)?void 0:t.status)===401&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"),"/login"!==window.location.pathname&&(window.location.href="/login")),Promise.reject((null==(r=e.response)||null==(a=r.data)?void 0:a.error)||e.message||"An unknown error occurred")});let o={auth:{login:e=>r.post("/login",e),register:e=>r.post("/register",e),getProfile:()=>r.get("/me"),logout:()=>r.post("/logout")},categories:{getAll:e=>r.get("/categories",{params:{type:e}}),getById:e=>r.get("/categories/".concat(e)),create:e=>r.post("/categories",e),update:(e,t)=>r.put("/categories/".concat(e),t),delete:e=>r.delete("/categories/".concat(e))},accounts:{getAll:()=>r.get("/accounts"),getById:e=>r.get("/accounts/".concat(e)),create:e=>r.post("/accounts",e),update:(e,t)=>r.put("/accounts/".concat(e),t),delete:e=>r.delete("/accounts/".concat(e))},transactions:{getAll:e=>r.get("/transactions",{params:e}),getById:e=>r.get("/transactions/".concat(e)),create:e=>r.post("/transactions",e),update:(e,t)=>r.put("/transactions/".concat(e),t),delete:e=>r.delete("/transactions/".concat(e))},reports:{getSummary:async e=>{try{let t=await r.get("/reports/summary",{params:e});if(!t||"object"!=typeof t)return console.warn("Invalid summary response, using default values"),{totalIncome:0,totalExpense:0,netBalance:0,topExpenseCategories:[],topIncomeCategories:[]};return t.data||t}catch(e){return console.error("Error fetching summary:",e),{totalIncome:0,totalExpense:0,netBalance:0,topExpenseCategories:[],topIncomeCategories:[]}}},getCategoryBreakdown:async e=>{try{let t=await r.get("/reports/category-breakdown",{params:e});if(!t||"object"!=typeof t)return{expenseCategories:[],incomeCategories:[]};return t.data||t}catch(e){return console.error("Error fetching category breakdown:",e),{expenseCategories:[],incomeCategories:[]}}},getMonthlyReport:async e=>{try{let t=await r.get("/reports/monthly",{params:e});if(!t||"object"!=typeof t)return{months:[]};return t.data||t}catch(e){return console.error("Error fetching monthly report:",e),{months:[]}}},getLocationSummary:async e=>{try{let t=await r.get("/reports/location-summary",{params:e});if(!t||"object"!=typeof t)return{locations:[]};return t.data||t}catch(e){return console.error("Error fetching location summary:",e),{locations:[]}}}},bankStatements:{upload:e=>r.post("/bank-statements/upload",e,{headers:{"Content-Type":"multipart/form-data"}}),import:e=>r.post("/bank-statements/import",e)}}},30285:(e,t,a)=>{a.d(t,{$:()=>d});var r=a(95155);a(12115);var o=a(99708),s=a(74466),n=a(59434);let i=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:a,size:s,asChild:d=!1,...l}=e,c=d?o.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,n.cn)(i({variant:a,size:s,className:t})),...l})}},59409:(e,t,a)=>{a.d(t,{bq:()=>u,eb:()=>g,gC:()=>p,l6:()=>l,yv:()=>c});var r=a(95155);a(12115);var o=a(38715),s=a(66474),n=a(5196),i=a(47863),d=a(59434);function l(e){let{...t}=e;return(0,r.jsx)(o.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,r.jsx)(o.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:a="default",children:n,...i}=e;return(0,r.jsxs)(o.l9,{"data-slot":"select-trigger","data-size":a,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...i,children:[n,(0,r.jsx)(o.In,{asChild:!0,children:(0,r.jsx)(s.A,{className:"size-4 opacity-50"})})]})}function p(e){let{className:t,children:a,position:s="popper",...n}=e;return(0,r.jsx)(o.ZL,{children:(0,r.jsxs)(o.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:s,...n,children:[(0,r.jsx)(m,{}),(0,r.jsx)(o.LM,{className:(0,d.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,r.jsx)(f,{})]})})}function g(e){let{className:t,children:a,...s}=e;return(0,r.jsxs)(o.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...s,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(o.VF,{children:(0,r.jsx)(n.A,{className:"size-4"})})}),(0,r.jsx)(o.p4,{children:a})]})}function m(e){let{className:t,...a}=e;return(0,r.jsx)(o.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(i.A,{className:"size-4"})})}function f(e){let{className:t,...a}=e;return(0,r.jsx)(o.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(s.A,{className:"size-4"})})}},59434:(e,t,a)=>{a.d(t,{cn:()=>s});var r=a(52596),o=a(39688);function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,o.QP)((0,r.$)(t))}},62523:(e,t,a)=>{a.d(t,{p:()=>s});var r=a(95155);a(12115);var o=a(59434);function s(e){let{className:t,type:a,...s}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,o.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...s})}},66695:(e,t,a)=>{a.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>i,Zp:()=>s,aR:()=>n,wL:()=>c});var r=a(95155);a(12115);var o=a(59434);function s(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,o.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,o.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,o.cn)("leading-none font-semibold",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,o.cn)("text-muted-foreground text-sm",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,o.cn)("px-6",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,o.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},85057:(e,t,a)=>{a.d(t,{J:()=>n});var r=a(95155);a(12115);var o=a(40968),s=a(59434);function n(e){let{className:t,...a}=e;return(0,r.jsx)(o.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}}}]);