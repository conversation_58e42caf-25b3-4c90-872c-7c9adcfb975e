"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[550],{5845:(e,t,n)=>{n.d(t,{i:()=>i});var r,o=n(12115),u=n(52712),l=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||u.N;function i({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[u,i,a]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),u=o.useRef(n),i=o.useRef(t);return l(()=>{i.current=t},[t]),o.useEffect(()=>{u.current!==n&&(i.current?.(n),u.current=n)},[n,u]),[n,r,i]}({defaultProp:t,onChange:n}),c=void 0!==e,s=c?e:u;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}return[s,o.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else i(t)},[c,e,i,a])]}Symbol("RADIX:SYNC_STATE")},28905:(e,t,n)=>{n.d(t,{C:()=>l});var r=n(12115),o=n(6101),u=n(52712),l=e=>{let{present:t,children:n}=e,l=function(e){var t,n;let[o,l]=r.useState(),a=r.useRef(null),c=r.useRef(e),s=r.useRef("none"),[f,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=i(a.current);s.current="mounted"===f?e:"none"},[f]),(0,u.N)(()=>{let t=a.current,n=c.current;if(n!==e){let r=s.current,o=i(t);e?d("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):n&&r!==o?d("ANIMATION_OUT"):d("UNMOUNT"),c.current=e}},[e,d]),(0,u.N)(()=>{if(o){var e;let t,n=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=i(a.current).includes(e.animationName);if(e.target===o&&r&&(d("ANIMATION_END"),!c.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},u=e=>{e.target===o&&(s.current=i(a.current))};return o.addEventListener("animationstart",u),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",u),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}d("ANIMATION_END")},[o,d]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:r.useCallback(e=>{a.current=e?getComputedStyle(e):null,l(e)},[])}}(t),a="function"==typeof n?n({present:l.isPresent}):r.Children.only(n),c=(0,o.s)(l.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof n||l.isPresent?r.cloneElement(a,{ref:c}):null};function i(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},37328:(e,t,n)=>{function r(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function o(e,t){var n=r(e,t,"get");return n.get?n.get.call(e):n.value}function u(e,t,n){var o=r(e,t,"set");if(o.set)o.set.call(e,n);else{if(!o.writable)throw TypeError("attempted to set read only private field");o.value=n}return n}n.d(t,{N:()=>d});var l,i=n(12115),a=n(46081),c=n(6101),s=n(99708),f=n(95155);function d(e){let t=e+"CollectionProvider",[n,r]=(0,a.A)(t),[o,u]=n(t,{collectionRef:{current:null},itemMap:new Map}),l=e=>{let{scope:t,children:n}=e,r=i.useRef(null),u=i.useRef(new Map).current;return(0,f.jsx)(o,{scope:t,itemMap:u,collectionRef:r,children:n})};l.displayName=t;let d=e+"CollectionSlot",m=(0,s.TL)(d),p=i.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=u(d,n),l=(0,c.s)(t,o.collectionRef);return(0,f.jsx)(m,{ref:l,children:r})});p.displayName=d;let v=e+"CollectionItemSlot",N="data-radix-collection-item",y=(0,s.TL)(v),M=i.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,l=i.useRef(null),a=(0,c.s)(t,l),s=u(v,n);return i.useEffect(()=>(s.itemMap.set(l,{ref:l,...o}),()=>void s.itemMap.delete(l))),(0,f.jsx)(y,{...{[N]:""},ref:a,children:r})});return M.displayName=v,[{Provider:l,Slot:p,ItemSlot:M},function(t){let n=u(e+"CollectionConsumer",t);return i.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(N,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}var m=new WeakMap;function p(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=v(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function v(e){return e!=e||0===e?0:Math.trunc(e)}l=new WeakMap},39033:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(12115);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},46081:(e,t,n)=>{n.d(t,{A:()=>l,q:()=>u});var r=n(12115),o=n(95155);function u(e,t){let n=r.createContext(t),u=e=>{let{children:t,...u}=e,l=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(n.Provider,{value:l,children:t})};return u.displayName=e+"Provider",[u,function(o){let u=r.useContext(n);if(u)return u;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function l(e,t=[]){let n=[],u=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return u.scopeName=e,[function(t,u){let l=r.createContext(u),i=n.length;n=[...n,u];let a=t=>{let{scope:n,children:u,...a}=t,c=n?.[e]?.[i]||l,s=r.useMemo(()=>a,Object.values(a));return(0,o.jsx)(c.Provider,{value:s,children:u})};return a.displayName=t+"Provider",[a,function(n,o){let a=o?.[e]?.[i]||l,c=r.useContext(a);if(c)return c;if(void 0!==u)return u;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(u,...t)]}},52712:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(12115),o=globalThis?.document?r.useLayoutEffect:()=>{}},61285:(e,t,n)=>{n.d(t,{B:()=>a});var r,o=n(12115),u=n(52712),l=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),i=0;function a(e){let[t,n]=o.useState(l());return(0,u.N)(()=>{e||n(e=>e??String(i++))},[e]),e||(t?`radix-${t}`:"")}},85185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},94315:(e,t,n)=>{n.d(t,{jH:()=>u});var r=n(12115);n(95155);var o=r.createContext(void 0);function u(e){let t=r.useContext(o);return e||t||"ltr"}}}]);