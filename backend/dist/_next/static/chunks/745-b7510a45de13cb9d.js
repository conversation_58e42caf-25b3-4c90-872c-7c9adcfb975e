"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[745],{381:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(19946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},22436:(e,r,n)=>{var t=n(12115),o="function"==typeof Object.is?Object.is:function(e,r){return e===r&&(0!==e||1/e==1/r)||e!=e&&r!=r},a=t.useState,l=t.useEffect,u=t.useLayoutEffect,i=t.useDebugValue;function d(e){var r=e.getSnapshot;e=e.value;try{var n=r();return!o(e,n)}catch(e){return!0}}var s="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,r){return r()}:function(e,r){var n=r(),t=a({inst:{value:n,getSnapshot:r}}),o=t[0].inst,s=t[1];return u(function(){o.value=n,o.getSnapshot=r,d(o)&&s({inst:o})},[e,n,r]),l(function(){return d(o)&&s({inst:o}),e(function(){d(o)&&s({inst:o})})},[e]),i(n),n};r.useSyncExternalStore=void 0!==t.useSyncExternalStore?t.useSyncExternalStore:s},34835:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(19946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},46308:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(19946).A)("receipt",[["path",{d:"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1Z",key:"q3az6g"}],["path",{d:"M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8",key:"1h4pet"}],["path",{d:"M12 17.5v-11",key:"1jc1ny"}]])},48698:(e,r,n)=>{n.d(r,{UC:()=>eZ,q7:()=>eY,JU:()=>eW,ZL:()=>eX,bL:()=>eB,wv:()=>e$,l9:()=>eq});var t=n(12115),o=n(85185),a=n(6101),l=n(46081),u=n(5845),i=n(63655),d=n(37328),s=n(94315),c=n(19178),p=n(92293),f=n(25519),v=n(61285),m=n(38795),h=n(34378),g=n(28905),y=n(89196),w=n(99708),x=n(39033),M=n(38168),b=n(93795),k=n(95155),C=["Enter"," "],j=["ArrowUp","PageDown","End"],R=["ArrowDown","PageUp","Home",...j],D={ltr:[...C,"ArrowRight"],rtl:[...C,"ArrowLeft"]},_={ltr:["ArrowLeft"],rtl:["ArrowRight"]},E="Menu",[S,A,N]=(0,d.N)(E),[I,P]=(0,l.A)(E,[N,m.Bk,y.RG]),L=(0,m.Bk)(),O=(0,y.RG)(),[T,F]=I(E),[G,K]=I(E),V=e=>{let{__scopeMenu:r,open:n=!1,children:o,dir:a,onOpenChange:l,modal:u=!0}=e,i=L(r),[d,c]=t.useState(null),p=t.useRef(!1),f=(0,x.c)(l),v=(0,s.jH)(a);return t.useEffect(()=>{let e=()=>{p.current=!0,document.addEventListener("pointerdown",r,{capture:!0,once:!0}),document.addEventListener("pointermove",r,{capture:!0,once:!0})},r=()=>p.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",r,{capture:!0}),document.removeEventListener("pointermove",r,{capture:!0})}},[]),(0,k.jsx)(m.bL,{...i,children:(0,k.jsx)(T,{scope:r,open:n,onOpenChange:f,content:d,onContentChange:c,children:(0,k.jsx)(G,{scope:r,onClose:t.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:p,dir:v,modal:u,children:o})})})};V.displayName=E;var H=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e,o=L(n);return(0,k.jsx)(m.Mz,{...o,...t,ref:r})});H.displayName="MenuAnchor";var z="MenuPortal",[U,B]=I(z,{forceMount:void 0}),q=e=>{let{__scopeMenu:r,forceMount:n,children:t,container:o}=e,a=F(z,r);return(0,k.jsx)(U,{scope:r,forceMount:n,children:(0,k.jsx)(g.C,{present:n||a.open,children:(0,k.jsx)(h.Z,{asChild:!0,container:o,children:t})})})};q.displayName=z;var X="MenuContent",[Z,W]=I(X),Y=t.forwardRef((e,r)=>{let n=B(X,e.__scopeMenu),{forceMount:t=n.forceMount,...o}=e,a=F(X,e.__scopeMenu),l=K(X,e.__scopeMenu);return(0,k.jsx)(S.Provider,{scope:e.__scopeMenu,children:(0,k.jsx)(g.C,{present:t||a.open,children:(0,k.jsx)(S.Slot,{scope:e.__scopeMenu,children:l.modal?(0,k.jsx)($,{...o,ref:r}):(0,k.jsx)(J,{...o,ref:r})})})})}),$=t.forwardRef((e,r)=>{let n=F(X,e.__scopeMenu),l=t.useRef(null),u=(0,a.s)(r,l);return t.useEffect(()=>{let e=l.current;if(e)return(0,M.Eq)(e)},[]),(0,k.jsx)(ee,{...e,ref:u,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),J=t.forwardRef((e,r)=>{let n=F(X,e.__scopeMenu);return(0,k.jsx)(ee,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),Q=(0,w.TL)("MenuContent.ScrollLock"),ee=t.forwardRef((e,r)=>{let{__scopeMenu:n,loop:l=!1,trapFocus:u,onOpenAutoFocus:i,onCloseAutoFocus:d,disableOutsidePointerEvents:s,onEntryFocus:v,onEscapeKeyDown:h,onPointerDownOutside:g,onFocusOutside:w,onInteractOutside:x,onDismiss:M,disableOutsideScroll:C,...D}=e,_=F(X,n),E=K(X,n),S=L(n),N=O(n),I=A(n),[P,T]=t.useState(null),G=t.useRef(null),V=(0,a.s)(r,G,_.onContentChange),H=t.useRef(0),z=t.useRef(""),U=t.useRef(0),B=t.useRef(null),q=t.useRef("right"),W=t.useRef(0),Y=C?b.A:t.Fragment,$=e=>{var r,n;let t=z.current+e,o=I().filter(e=>!e.disabled),a=document.activeElement,l=null==(r=o.find(e=>e.ref.current===a))?void 0:r.textValue,u=function(e,r,n){var t;let o=r.length>1&&Array.from(r).every(e=>e===r[0])?r[0]:r,a=n?e.indexOf(n):-1,l=(t=Math.max(a,0),e.map((r,n)=>e[(t+n)%e.length]));1===o.length&&(l=l.filter(e=>e!==n));let u=l.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return u!==n?u:void 0}(o.map(e=>e.textValue),t,l),i=null==(n=o.find(e=>e.textValue===u))?void 0:n.ref.current;!function e(r){z.current=r,window.clearTimeout(H.current),""!==r&&(H.current=window.setTimeout(()=>e(""),1e3))}(t),i&&setTimeout(()=>i.focus())};t.useEffect(()=>()=>window.clearTimeout(H.current),[]),(0,p.Oh)();let J=t.useCallback(e=>{var r,n;return q.current===(null==(r=B.current)?void 0:r.side)&&function(e,r){return!!r&&function(e,r){let{x:n,y:t}=e,o=!1;for(let e=0,a=r.length-1;e<r.length;a=e++){let l=r[e],u=r[a],i=l.x,d=l.y,s=u.x,c=u.y;d>t!=c>t&&n<(s-i)*(t-d)/(c-d)+i&&(o=!o)}return o}({x:e.clientX,y:e.clientY},r)}(e,null==(n=B.current)?void 0:n.area)},[]);return(0,k.jsx)(Z,{scope:n,searchRef:z,onItemEnter:t.useCallback(e=>{J(e)&&e.preventDefault()},[J]),onItemLeave:t.useCallback(e=>{var r;J(e)||(null==(r=G.current)||r.focus(),T(null))},[J]),onTriggerLeave:t.useCallback(e=>{J(e)&&e.preventDefault()},[J]),pointerGraceTimerRef:U,onPointerGraceIntentChange:t.useCallback(e=>{B.current=e},[]),children:(0,k.jsx)(Y,{...C?{as:Q,allowPinchZoom:!0}:void 0,children:(0,k.jsx)(f.n,{asChild:!0,trapped:u,onMountAutoFocus:(0,o.m)(i,e=>{var r;e.preventDefault(),null==(r=G.current)||r.focus({preventScroll:!0})}),onUnmountAutoFocus:d,children:(0,k.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:h,onPointerDownOutside:g,onFocusOutside:w,onInteractOutside:x,onDismiss:M,children:(0,k.jsx)(y.bL,{asChild:!0,...N,dir:E.dir,orientation:"vertical",loop:l,currentTabStopId:P,onCurrentTabStopIdChange:T,onEntryFocus:(0,o.m)(v,e=>{E.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,k.jsx)(m.UC,{role:"menu","aria-orientation":"vertical","data-state":eR(_.open),"data-radix-menu-content":"",dir:E.dir,...S,...D,ref:V,style:{outline:"none",...D.style},onKeyDown:(0,o.m)(D.onKeyDown,e=>{let r=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,t=1===e.key.length;r&&("Tab"===e.key&&e.preventDefault(),!n&&t&&$(e.key));let o=G.current;if(e.target!==o||!R.includes(e.key))return;e.preventDefault();let a=I().filter(e=>!e.disabled).map(e=>e.ref.current);j.includes(e.key)&&a.reverse(),function(e){let r=document.activeElement;for(let n of e)if(n===r||(n.focus(),document.activeElement!==r))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(H.current),z.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eE(e=>{let r=e.target,n=W.current!==e.clientX;e.currentTarget.contains(r)&&n&&(q.current=e.clientX>W.current?"right":"left",W.current=e.clientX)}))})})})})})})});Y.displayName=X;var er=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,k.jsx)(i.sG.div,{role:"group",...t,ref:r})});er.displayName="MenuGroup";var en=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,k.jsx)(i.sG.div,{...t,ref:r})});en.displayName="MenuLabel";var et="MenuItem",eo="menu.itemSelect",ea=t.forwardRef((e,r)=>{let{disabled:n=!1,onSelect:l,...u}=e,d=t.useRef(null),s=K(et,e.__scopeMenu),c=W(et,e.__scopeMenu),p=(0,a.s)(r,d),f=t.useRef(!1);return(0,k.jsx)(el,{...u,ref:p,disabled:n,onClick:(0,o.m)(e.onClick,()=>{let e=d.current;if(!n&&e){let r=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>null==l?void 0:l(e),{once:!0}),(0,i.hO)(e,r),r.defaultPrevented?f.current=!1:s.onClose()}}),onPointerDown:r=>{var n;null==(n=e.onPointerDown)||n.call(e,r),f.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var r;f.current||null==(r=e.currentTarget)||r.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=""!==c.searchRef.current;n||r&&" "===e.key||C.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ea.displayName=et;var el=t.forwardRef((e,r)=>{let{__scopeMenu:n,disabled:l=!1,textValue:u,...d}=e,s=W(et,n),c=O(n),p=t.useRef(null),f=(0,a.s)(r,p),[v,m]=t.useState(!1),[h,g]=t.useState("");return t.useEffect(()=>{let e=p.current;if(e){var r;g((null!=(r=e.textContent)?r:"").trim())}},[d.children]),(0,k.jsx)(S.ItemSlot,{scope:n,disabled:l,textValue:null!=u?u:h,children:(0,k.jsx)(y.q7,{asChild:!0,...c,focusable:!l,children:(0,k.jsx)(i.sG.div,{role:"menuitem","data-highlighted":v?"":void 0,"aria-disabled":l||void 0,"data-disabled":l?"":void 0,...d,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,eE(e=>{l?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eE(e=>s.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>m(!0)),onBlur:(0,o.m)(e.onBlur,()=>m(!1))})})})}),eu=t.forwardRef((e,r)=>{let{checked:n=!1,onCheckedChange:t,...a}=e;return(0,k.jsx)(em,{scope:e.__scopeMenu,checked:n,children:(0,k.jsx)(ea,{role:"menuitemcheckbox","aria-checked":eD(n)?"mixed":n,...a,ref:r,"data-state":e_(n),onSelect:(0,o.m)(a.onSelect,()=>null==t?void 0:t(!!eD(n)||!n),{checkForDefaultPrevented:!1})})})});eu.displayName="MenuCheckboxItem";var ei="MenuRadioGroup",[ed,es]=I(ei,{value:void 0,onValueChange:()=>{}}),ec=t.forwardRef((e,r)=>{let{value:n,onValueChange:t,...o}=e,a=(0,x.c)(t);return(0,k.jsx)(ed,{scope:e.__scopeMenu,value:n,onValueChange:a,children:(0,k.jsx)(er,{...o,ref:r})})});ec.displayName=ei;var ep="MenuRadioItem",ef=t.forwardRef((e,r)=>{let{value:n,...t}=e,a=es(ep,e.__scopeMenu),l=n===a.value;return(0,k.jsx)(em,{scope:e.__scopeMenu,checked:l,children:(0,k.jsx)(ea,{role:"menuitemradio","aria-checked":l,...t,ref:r,"data-state":e_(l),onSelect:(0,o.m)(t.onSelect,()=>{var e;return null==(e=a.onValueChange)?void 0:e.call(a,n)},{checkForDefaultPrevented:!1})})})});ef.displayName=ep;var ev="MenuItemIndicator",[em,eh]=I(ev,{checked:!1}),eg=t.forwardRef((e,r)=>{let{__scopeMenu:n,forceMount:t,...o}=e,a=eh(ev,n);return(0,k.jsx)(g.C,{present:t||eD(a.checked)||!0===a.checked,children:(0,k.jsx)(i.sG.span,{...o,ref:r,"data-state":e_(a.checked)})})});eg.displayName=ev;var ey=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,k.jsx)(i.sG.div,{role:"separator","aria-orientation":"horizontal",...t,ref:r})});ey.displayName="MenuSeparator";var ew=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e,o=L(n);return(0,k.jsx)(m.i3,{...o,...t,ref:r})});ew.displayName="MenuArrow";var[ex,eM]=I("MenuSub"),eb="MenuSubTrigger",ek=t.forwardRef((e,r)=>{let n=F(eb,e.__scopeMenu),l=K(eb,e.__scopeMenu),u=eM(eb,e.__scopeMenu),i=W(eb,e.__scopeMenu),d=t.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:c}=i,p={__scopeMenu:e.__scopeMenu},f=t.useCallback(()=>{d.current&&window.clearTimeout(d.current),d.current=null},[]);return t.useEffect(()=>f,[f]),t.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),c(null)}},[s,c]),(0,k.jsx)(H,{asChild:!0,...p,children:(0,k.jsx)(el,{id:u.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":u.contentId,"data-state":eR(n.open),...e,ref:(0,a.t)(r,u.onTriggerChange),onClick:r=>{var t;null==(t=e.onClick)||t.call(e,r),e.disabled||r.defaultPrevented||(r.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eE(r=>{i.onItemEnter(r),!r.defaultPrevented&&(e.disabled||n.open||d.current||(i.onPointerGraceIntentChange(null),d.current=window.setTimeout(()=>{n.onOpenChange(!0),f()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eE(e=>{var r,t;f();let o=null==(r=n.content)?void 0:r.getBoundingClientRect();if(o){let r=null==(t=n.content)?void 0:t.dataset.side,a="right"===r,l=o[a?"left":"right"],u=o[a?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:l,y:o.top},{x:u,y:o.top},{x:u,y:o.bottom},{x:l,y:o.bottom}],side:r}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,r=>{let t=""!==i.searchRef.current;if(!e.disabled&&(!t||" "!==r.key)&&D[l.dir].includes(r.key)){var o;n.onOpenChange(!0),null==(o=n.content)||o.focus(),r.preventDefault()}})})})});ek.displayName=eb;var eC="MenuSubContent",ej=t.forwardRef((e,r)=>{let n=B(X,e.__scopeMenu),{forceMount:l=n.forceMount,...u}=e,i=F(X,e.__scopeMenu),d=K(X,e.__scopeMenu),s=eM(eC,e.__scopeMenu),c=t.useRef(null),p=(0,a.s)(r,c);return(0,k.jsx)(S.Provider,{scope:e.__scopeMenu,children:(0,k.jsx)(g.C,{present:l||i.open,children:(0,k.jsx)(S.Slot,{scope:e.__scopeMenu,children:(0,k.jsx)(ee,{id:s.contentId,"aria-labelledby":s.triggerId,...u,ref:p,align:"start",side:"rtl"===d.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var r;d.isUsingKeyboardRef.current&&(null==(r=c.current)||r.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==s.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{d.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=e.currentTarget.contains(e.target),n=_[d.dir].includes(e.key);if(r&&n){var t;i.onOpenChange(!1),null==(t=s.trigger)||t.focus(),e.preventDefault()}})})})})})});function eR(e){return e?"open":"closed"}function eD(e){return"indeterminate"===e}function e_(e){return eD(e)?"indeterminate":e?"checked":"unchecked"}function eE(e){return r=>"mouse"===r.pointerType?e(r):void 0}ej.displayName=eC;var eS="DropdownMenu",[eA,eN]=(0,l.A)(eS,[P]),eI=P(),[eP,eL]=eA(eS),eO=e=>{let{__scopeDropdownMenu:r,children:n,dir:o,open:a,defaultOpen:l,onOpenChange:i,modal:d=!0}=e,s=eI(r),c=t.useRef(null),[p,f]=(0,u.i)({prop:a,defaultProp:null!=l&&l,onChange:i,caller:eS});return(0,k.jsx)(eP,{scope:r,triggerId:(0,v.B)(),triggerRef:c,contentId:(0,v.B)(),open:p,onOpenChange:f,onOpenToggle:t.useCallback(()=>f(e=>!e),[f]),modal:d,children:(0,k.jsx)(V,{...s,open:p,onOpenChange:f,dir:o,modal:d,children:n})})};eO.displayName=eS;var eT="DropdownMenuTrigger",eF=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,disabled:t=!1,...l}=e,u=eL(eT,n),d=eI(n);return(0,k.jsx)(H,{asChild:!0,...d,children:(0,k.jsx)(i.sG.button,{type:"button",id:u.triggerId,"aria-haspopup":"menu","aria-expanded":u.open,"aria-controls":u.open?u.contentId:void 0,"data-state":u.open?"open":"closed","data-disabled":t?"":void 0,disabled:t,...l,ref:(0,a.t)(r,u.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!t&&0===e.button&&!1===e.ctrlKey&&(u.onOpenToggle(),u.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!t&&(["Enter"," "].includes(e.key)&&u.onOpenToggle(),"ArrowDown"===e.key&&u.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eF.displayName=eT;var eG=e=>{let{__scopeDropdownMenu:r,...n}=e,t=eI(r);return(0,k.jsx)(q,{...t,...n})};eG.displayName="DropdownMenuPortal";var eK="DropdownMenuContent",eV=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...a}=e,l=eL(eK,n),u=eI(n),i=t.useRef(!1);return(0,k.jsx)(Y,{id:l.contentId,"aria-labelledby":l.triggerId,...u,...a,ref:r,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var r;i.current||null==(r=l.triggerRef.current)||r.focus(),i.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let r=e.detail.originalEvent,n=0===r.button&&!0===r.ctrlKey,t=2===r.button||n;(!l.modal||t)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eV.displayName=eK,t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eI(n);return(0,k.jsx)(er,{...o,...t,ref:r})}).displayName="DropdownMenuGroup";var eH=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eI(n);return(0,k.jsx)(en,{...o,...t,ref:r})});eH.displayName="DropdownMenuLabel";var ez=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eI(n);return(0,k.jsx)(ea,{...o,...t,ref:r})});ez.displayName="DropdownMenuItem",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eI(n);return(0,k.jsx)(eu,{...o,...t,ref:r})}).displayName="DropdownMenuCheckboxItem",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eI(n);return(0,k.jsx)(ec,{...o,...t,ref:r})}).displayName="DropdownMenuRadioGroup",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eI(n);return(0,k.jsx)(ef,{...o,...t,ref:r})}).displayName="DropdownMenuRadioItem",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eI(n);return(0,k.jsx)(eg,{...o,...t,ref:r})}).displayName="DropdownMenuItemIndicator";var eU=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eI(n);return(0,k.jsx)(ey,{...o,...t,ref:r})});eU.displayName="DropdownMenuSeparator",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eI(n);return(0,k.jsx)(ew,{...o,...t,ref:r})}).displayName="DropdownMenuArrow",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eI(n);return(0,k.jsx)(ek,{...o,...t,ref:r})}).displayName="DropdownMenuSubTrigger",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eI(n);return(0,k.jsx)(ej,{...o,...t,ref:r,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var eB=eO,eq=eF,eX=eG,eZ=eV,eW=eH,eY=ez,e$=eU},49033:(e,r,n)=>{e.exports=n(22436)},51362:(e,r,n)=>{n.d(r,{D:()=>u});var t=n(12115),o="(prefers-color-scheme: dark)",a=t.createContext(void 0),l={setTheme:e=>{},themes:[]},u=()=>{var e;return null!=(e=t.useContext(a))?e:l},i=null,d=(e,r)=>{let n;try{n=localStorage.getItem(e)||void 0}catch(e){}return n||r},s=e=>{let r=document.createElement("style");return e&&r.setAttribute("nonce",e),r.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(r),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(r)},1)}},c=e=>(e||(e=window.matchMedia(o)),e.matches?"dark":"light")},53817:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(19946).A)("tags",[["path",{d:"m15 5 6.3 6.3a2.4 2.4 0 0 1 0 3.4L17 19",key:"1cbfv1"}],["path",{d:"M9.586 5.586A2 2 0 0 0 8.172 5H3a1 1 0 0 0-1 1v5.172a2 2 0 0 0 .586 1.414L8.29 18.29a2.426 2.426 0 0 0 3.42 0l3.58-3.58a2.426 2.426 0 0 0 0-3.42z",key:"135mg7"}],["circle",{cx:"6.5",cy:"9.5",r:".5",fill:"currentColor",key:"5pm5xn"}]])},54011:(e,r,n)=>{n.d(r,{H4:()=>C,_V:()=>k,bL:()=>b});var t=n(12115),o=n(46081),a=n(39033),l=n(52712),u=n(63655),i=n(49033);function d(){return()=>{}}var s=n(95155),c="Avatar",[p,f]=(0,o.A)(c),[v,m]=p(c),h=t.forwardRef((e,r)=>{let{__scopeAvatar:n,...o}=e,[a,l]=t.useState("idle");return(0,s.jsx)(v,{scope:n,imageLoadingStatus:a,onImageLoadingStatusChange:l,children:(0,s.jsx)(u.sG.span,{...o,ref:r})})});h.displayName=c;var g="AvatarImage",y=t.forwardRef((e,r)=>{let{__scopeAvatar:n,src:o,onLoadingStatusChange:c=()=>{},...p}=e,f=m(g,n),v=function(e,r){let{referrerPolicy:n,crossOrigin:o}=r,a=(0,i.useSyncExternalStore)(d,()=>!0,()=>!1),u=t.useRef(null),s=a?(u.current||(u.current=new window.Image),u.current):null,[c,p]=t.useState(()=>M(s,e));return(0,l.N)(()=>{p(M(s,e))},[s,e]),(0,l.N)(()=>{let e=e=>()=>{p(e)};if(!s)return;let r=e("loaded"),t=e("error");return s.addEventListener("load",r),s.addEventListener("error",t),n&&(s.referrerPolicy=n),"string"==typeof o&&(s.crossOrigin=o),()=>{s.removeEventListener("load",r),s.removeEventListener("error",t)}},[s,o,n]),c}(o,p),h=(0,a.c)(e=>{c(e),f.onImageLoadingStatusChange(e)});return(0,l.N)(()=>{"idle"!==v&&h(v)},[v,h]),"loaded"===v?(0,s.jsx)(u.sG.img,{...p,ref:r,src:o}):null});y.displayName=g;var w="AvatarFallback",x=t.forwardRef((e,r)=>{let{__scopeAvatar:n,delayMs:o,...a}=e,l=m(w,n),[i,d]=t.useState(void 0===o);return t.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>d(!0),o);return()=>window.clearTimeout(e)}},[o]),i&&"loaded"!==l.imageLoadingStatus?(0,s.jsx)(u.sG.span,{...a,ref:r}):null});function M(e,r){return e?r?(e.src!==r&&(e.src=r),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}x.displayName=w;var b=h,k=y,C=x},57434:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},71007:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},73783:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(19946).A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},74466:(e,r,n)=>{n.d(r,{F:()=>l});var t=n(52596);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=t.$,l=(e,r)=>n=>{var t;if((null==r?void 0:r.variants)==null)return a(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:l,defaultVariants:u}=r,i=Object.keys(l).map(e=>{let r=null==n?void 0:n[e],t=null==u?void 0:u[e];if(null===r)return null;let a=o(r)||o(t);return l[e][a]}),d=n&&Object.entries(n).reduce((e,r)=>{let[n,t]=r;return void 0===t||(e[n]=t),e},{});return a(e,i,null==r||null==(t=r.compoundVariants)?void 0:t.reduce((e,r)=>{let{class:n,className:t,...o}=r;return Object.entries(o).every(e=>{let[r,n]=e;return Array.isArray(n)?n.includes({...u,...d}[r]):({...u,...d})[r]===n})?[...e,n,t]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},74783:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(19946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},81586:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(19946).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])}}]);