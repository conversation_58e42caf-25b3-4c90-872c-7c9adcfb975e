(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[305],{4879:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>V});var s=a(95155),r=a(12115),n=a(35695),o=a(27600),i=a(6874),l=a.n(i),c=a(30285),d=a(54011),u=a(59434);function m(e){let{className:t,...a}=e;return(0,s.jsx)(d.bL,{"data-slot":"avatar",className:(0,u.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...a})}function h(e){let{className:t,...a}=e;return(0,s.jsx)(d._V,{"data-slot":"avatar-image",className:(0,u.cn)("aspect-square size-full",t),...a})}function g(e){let{className:t,...a}=e;return(0,s.jsx)(d.H4,{"data-slot":"avatar-fallback",className:(0,u.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...a})}var f=a(48698);function p(e){let{...t}=e;return(0,s.jsx)(f.bL,{"data-slot":"dropdown-menu",...t})}function x(e){let{...t}=e;return(0,s.jsx)(f.l9,{"data-slot":"dropdown-menu-trigger",...t})}function v(e){let{className:t,sideOffset:a=4,...r}=e;return(0,s.jsx)(f.ZL,{children:(0,s.jsx)(f.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,u.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...r})})}function b(e){let{className:t,inset:a,variant:r="default",...n}=e;return(0,s.jsx)(f.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":r,className:(0,u.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...n})}function j(e){let{className:t,inset:a,...r}=e;return(0,s.jsx)(f.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,u.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...r})}function y(e){let{className:t,...a}=e;return(0,s.jsx)(f.wv,{"data-slot":"dropdown-menu-separator",className:(0,u.cn)("bg-border -mx-1 my-1 h-px",t),...a})}var w=a(15452),N=a(54416);function k(e){let{...t}=e;return(0,s.jsx)(w.bL,{"data-slot":"sheet",...t})}function A(e){let{...t}=e;return(0,s.jsx)(w.l9,{"data-slot":"sheet-trigger",...t})}function z(e){let{...t}=e;return(0,s.jsx)(w.ZL,{"data-slot":"sheet-portal",...t})}function C(e){let{className:t,...a}=e;return(0,s.jsx)(w.hJ,{"data-slot":"sheet-overlay",className:(0,u.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function S(e){let{className:t,children:a,side:r="right",...n}=e;return(0,s.jsxs)(z,{children:[(0,s.jsx)(C,{}),(0,s.jsxs)(w.UC,{"data-slot":"sheet-content",className:(0,u.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===r&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===r&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===r&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===r&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",t),...n,children:[a,(0,s.jsxs)(w.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,s.jsx)(N.A,{className:"size-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function I(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"sheet-header",className:(0,u.cn)("flex flex-col gap-1.5 p-4",t),...a})}function _(e){let{className:t,...a}=e;return(0,s.jsx)(w.hE,{"data-slot":"sheet-title",className:(0,u.cn)("text-foreground font-semibold",t),...a})}var E=a(73783),P=a(46308),L=a(57434),B=a(81586),O=a(53817),R=a(72713),U=a(381),$=a(74783),J=a(71007),T=a(34835);let q=e=>{let{href:t,icon:a,title:r,isActive:n,isMobile:o=!1}=e;return(0,s.jsxs)(l(),{href:t,className:"flex items-center gap-3 rounded-lg px-3 py-2 transition-all ".concat(n?"bg-primary text-primary-foreground":"hover:bg-muted"," ").concat(o?"text-lg py-3":""),children:[a,(0,s.jsx)("span",{children:r})]})};function D(e){var t;let{children:a}=e,{user:i,logout:d}=(0,o.A)(),u=(0,n.usePathname)(),[f,w]=(0,r.useState)(!1),N=[{href:"/dashboard",icon:(0,s.jsx)(E.A,{size:20}),title:"Dashboard"},{href:"/transactions",icon:(0,s.jsx)(P.A,{size:20}),title:"Transactions"},{href:"/bank-statements",icon:(0,s.jsx)(L.A,{size:20}),title:"Bank Statements"},{href:"/accounts",icon:(0,s.jsx)(B.A,{size:20}),title:"Accounts"},{href:"/categories",icon:(0,s.jsx)(O.A,{size:20}),title:"Categories"},{href:"/reports",icon:(0,s.jsx)(R.A,{size:20}),title:"Reports"},{href:"/settings",icon:(0,s.jsx)(U.A,{size:20}),title:"Settings"}];return(0,s.jsxs)("div",{className:"flex min-h-screen flex-col",children:[(0,s.jsxs)("header",{className:"sticky top-0 z-30 flex h-16 items-center gap-4 border-b bg-background px-4 md:px-6",children:[(0,s.jsxs)(k,{open:f,onOpenChange:w,children:[(0,s.jsx)(A,{asChild:!0,className:"md:hidden",children:(0,s.jsxs)(c.$,{variant:"outline",size:"icon",className:"shrink-0",children:[(0,s.jsx)($.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle menu"})]})}),(0,s.jsxs)(S,{side:"left",className:"w-64 sm:max-w-xs",children:[(0,s.jsx)(I,{className:"pb-6",children:(0,s.jsx)(_,{className:"text-xl",children:"Finance Notebook"})}),(0,s.jsx)("nav",{className:"grid gap-2 text-lg font-medium",children:N.map(e=>(0,s.jsx)(q,{href:e.href,icon:e.icon,title:e.title,isActive:u===e.href,isMobile:!0},e.href))})]})]}),(0,s.jsxs)(l(),{href:"/dashboard",className:"flex items-center gap-2 font-semibold",children:[(0,s.jsx)(R.A,{className:"h-6 w-6"}),(0,s.jsx)("span",{className:"hidden md:inline-block",children:"Finance Notebook"})]}),(0,s.jsx)("div",{className:"ml-auto flex items-center gap-2",children:(0,s.jsxs)(p,{children:[(0,s.jsx)(x,{asChild:!0,children:(0,s.jsx)(c.$,{variant:"ghost",className:"relative h-9 w-9 rounded-full",children:(0,s.jsxs)(m,{className:"h-9 w-9",children:[(0,s.jsx)(h,{src:"",alt:(null==i?void 0:i.name)||"User"}),(0,s.jsx)(g,{children:(null==i||null==(t=i.name)?void 0:t.charAt(0))||"U"})]})})}),(0,s.jsxs)(v,{className:"w-56",align:"end",forceMount:!0,children:[(0,s.jsx)(j,{children:(0,s.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium leading-none",children:null==i?void 0:i.name}),(0,s.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:null==i?void 0:i.email})]})}),(0,s.jsx)(y,{}),(0,s.jsx)(b,{asChild:!0,children:(0,s.jsxs)(l(),{href:"/profile",className:"cursor-pointer",children:[(0,s.jsx)(J.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Profile"})]})}),(0,s.jsx)(b,{asChild:!0,children:(0,s.jsxs)(l(),{href:"/settings",className:"cursor-pointer",children:[(0,s.jsx)(U.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Settings"})]})}),(0,s.jsx)(y,{}),(0,s.jsxs)(b,{className:"cursor-pointer text-red-600 focus:text-red-600",onClick:()=>d(),children:[(0,s.jsx)(T.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Log out"})]})]})]})})]}),(0,s.jsxs)("div",{className:"flex flex-1",children:[(0,s.jsx)("aside",{className:"hidden w-64 shrink-0 border-r md:block",children:(0,s.jsx)("div",{className:"sticky top-16 overflow-auto p-4 h-[calc(100vh-4rem)]",children:(0,s.jsx)("nav",{className:"grid gap-2 text-sm font-medium",children:N.map(e=>(0,s.jsx)(q,{href:e.href,icon:e.icon,title:e.title,isActive:u===e.href},e.href))})})}),(0,s.jsx)("main",{className:"flex-1 p-4 md:p-6",children:a})]})]})}var F=a(89074);function M(e){let{children:t}=e,{isAuthenticated:a,isLoading:i}=(0,o.A)(),l=(0,n.useRouter)();return((0,r.useEffect)(()=>{i||a||l.push("/login")},[a,i,l]),i)?(0,s.jsx)("div",{className:"flex h-screen w-full items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary"})}):a?(0,s.jsx)(D,{children:t}):null}function V(e){let{children:t}=e;return(0,s.jsxs)(o.O,{children:[(0,s.jsx)(M,{children:t}),(0,s.jsx)(F.l,{})]})}},25453:(e,t,a)=>{Promise.resolve().then(a.bind(a,4879))},25731:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let s=a(23464).A.create({baseURL:"https://localhost:8008/api/v1",headers:{"Content-Type":"application/json"}});s.interceptors.request.use(e=>{{let t=localStorage.getItem("auth_token");t&&e.headers&&(e.headers.Authorization="Bearer ".concat(t))}return e},e=>Promise.reject(e)),s.interceptors.response.use(e=>(console.log("API Response:",e),e.data&&e.data.data)?e.data.data:e.data?e.data:(console.warn("Empty response data"),{}),e=>{var t,a,s;return console.error("API Error:",e),(null==(t=e.response)?void 0:t.status)===401&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"),"/login"!==window.location.pathname&&(window.location.href="/login")),Promise.reject((null==(s=e.response)||null==(a=s.data)?void 0:a.error)||e.message||"An unknown error occurred")});let r={auth:{login:e=>s.post("/login",e),register:e=>s.post("/register",e),getProfile:()=>s.get("/me"),logout:()=>s.post("/logout")},categories:{getAll:e=>s.get("/categories",{params:{type:e}}),getById:e=>s.get("/categories/".concat(e)),create:e=>s.post("/categories",e),update:(e,t)=>s.put("/categories/".concat(e),t),delete:e=>s.delete("/categories/".concat(e))},accounts:{getAll:()=>s.get("/accounts"),getById:e=>s.get("/accounts/".concat(e)),create:e=>s.post("/accounts",e),update:(e,t)=>s.put("/accounts/".concat(e),t),delete:e=>s.delete("/accounts/".concat(e))},transactions:{getAll:e=>s.get("/transactions",{params:e}),getById:e=>s.get("/transactions/".concat(e)),create:e=>s.post("/transactions",e),update:(e,t)=>s.put("/transactions/".concat(e),t),delete:e=>s.delete("/transactions/".concat(e))},reports:{getSummary:async e=>{try{let t=await s.get("/reports/summary",{params:e});if(!t||"object"!=typeof t)return console.warn("Invalid summary response, using default values"),{totalIncome:0,totalExpense:0,netBalance:0,topExpenseCategories:[],topIncomeCategories:[]};return t}catch(e){return console.error("Error fetching summary:",e),{totalIncome:0,totalExpense:0,netBalance:0,topExpenseCategories:[],topIncomeCategories:[]}}},getCategoryBreakdown:async e=>{try{let t=await s.get("/reports/category-breakdown",{params:e});if(!t||"object"!=typeof t)return{expenseCategories:[],incomeCategories:[]};return t}catch(e){return console.error("Error fetching category breakdown:",e),{expenseCategories:[],incomeCategories:[]}}},getMonthlyReport:async e=>{try{let t=await s.get("/reports/monthly",{params:e});if(!t||"object"!=typeof t)return{months:[]};return t}catch(e){return console.error("Error fetching monthly report:",e),{months:[]}}},getLocationSummary:async e=>{try{let t=await s.get("/reports/location-summary",{params:e});if(!t||"object"!=typeof t)return{locations:[]};return t}catch(e){return console.error("Error fetching location summary:",e),{locations:[]}}}},bankStatements:{upload:e=>s.post("/bank-statements/upload",e,{headers:{"Content-Type":"multipart/form-data"}}),import:e=>s.post("/bank-statements/import",e)}}},27600:(e,t,a)=>{"use strict";a.d(t,{A:()=>c,O:()=>l});var s=a(95155),r=a(12115),n=a(25731),o=a(35695);let i=(0,r.createContext)(void 0);function l(e){let{children:t}=e,[a,l]=(0,r.useState)(null),[c,d]=(0,r.useState)(!0),u=(0,o.useRouter)();(0,r.useEffect)(()=>{(async()=>{try{if(!localStorage.getItem("auth_token"))return void d(!1);let e=await n.A.auth.getProfile();l(e)}catch(e){localStorage.removeItem("auth_token"),localStorage.removeItem("user")}finally{d(!1)}})()},[]);let m=async(e,t)=>{d(!0);try{let a=await n.A.auth.login({username:e,password:t});localStorage.setItem("auth_token",a.token),localStorage.setItem("user",JSON.stringify(a.user)),l(a.user),u.push("/dashboard")}catch(e){throw e}finally{d(!1)}},h=async e=>{d(!0);try{let t=await n.A.auth.register(e);localStorage.setItem("auth_token",t.token),localStorage.setItem("user",JSON.stringify(t.user)),l(t.user),u.push("/dashboard")}catch(e){throw e}finally{d(!1)}},g=async()=>{d(!0);try{await n.A.auth.logout()}catch(e){console.error("Logout error:",e)}finally{localStorage.removeItem("auth_token"),localStorage.removeItem("user"),l(null),u.push("/login"),d(!1)}};return(0,s.jsx)(i.Provider,{value:{user:a,isLoading:c,isAuthenticated:!!a,login:m,register:h,logout:g},children:t})}function c(){let e=(0,r.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},30285:(e,t,a)=>{"use strict";a.d(t,{$:()=>l});var s=a(95155);a(12115);var r=a(99708),n=a(74466),o=a(59434);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:a,size:n,asChild:l=!1,...c}=e,d=l?r.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,o.cn)(i({variant:a,size:n,className:t})),...c})}},59434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n});var s=a(52596),r=a(39688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}},89074:(e,t,a)=>{"use strict";a.d(t,{l:()=>o});var s=a(95155),r=a(51362),n=a(56671);let o=e=>{let{...t}=e,{theme:a="system"}=(0,r.D)();return(0,s.jsx)(n.l$,{theme:a,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...t})}}},e=>{var t=t=>e(e.s=t);e.O(0,[464,204,671,550,26,498,183,441,684,358],()=>t(25453)),_N_E=e.O()}]);