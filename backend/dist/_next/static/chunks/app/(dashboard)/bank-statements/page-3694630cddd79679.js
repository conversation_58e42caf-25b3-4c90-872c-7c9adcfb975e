(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[650],{34044:(t,e,a)=>{"use strict";a.r(e),a.d(e,{default:()=>d});var s=a(95155),r=a(12115),n=a(66695);function d(){let[t,e]=(0,r.useState)("Bank Statements page is loading...");return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Bank Statements"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Upload your bank statement PDF to automatically extract and import transactions"})]}),(0,s.jsxs)(n.Zp,{children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsx)(n.ZB,{children:"Test Page"}),(0,s.jsx)(n.BT,{children:"This is a simplified version to test if the page loads correctly"})]}),(0,s.jsxs)(n.Wu,{children:[(0,s.jsx)("p",{children:t}),(0,s.jsx)("button",{onClick:()=>e("Button clicked! Page is working."),className:"mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Test Button"})]})]})]})}},59434:(t,e,a)=>{"use strict";a.d(e,{cn:()=>n});var s=a(52596),r=a(39688);function n(){for(var t=arguments.length,e=Array(t),a=0;a<t;a++)e[a]=arguments[a];return(0,r.QP)((0,s.$)(e))}},66695:(t,e,a)=>{"use strict";a.d(e,{BT:()=>o,Wu:()=>i,ZB:()=>c,Zp:()=>n,aR:()=>d,wL:()=>l});var s=a(95155);a(12115);var r=a(59434);function n(t){let{className:e,...a}=t;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...a})}function d(t){let{className:e,...a}=t;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...a})}function c(t){let{className:e,...a}=t;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...a})}function o(t){let{className:e,...a}=t;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...a})}function i(t){let{className:e,...a}=t;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...a})}function l(t){let{className:e,...a}=t;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",e),...a})}},98250:(t,e,a)=>{Promise.resolve().then(a.bind(a,34044))}},t=>{var e=e=>t(t.s=e);t.O(0,[277,441,684,358],()=>e(98250)),_N_E=t.O()}]);