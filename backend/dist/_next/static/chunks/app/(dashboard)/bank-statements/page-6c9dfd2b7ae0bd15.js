(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[650],{25731:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let a=r(23464).A.create({baseURL:"https://localhost:8008/api/v1",headers:{"Content-Type":"application/json"}});a.interceptors.request.use(e=>{{let t=localStorage.getItem("auth_token");t&&e.headers&&(e.headers.Authorization="Bearer ".concat(t))}return e},e=>Promise.reject(e)),a.interceptors.response.use(e=>(console.log("API Response:",e),e.data&&e.data.data)?e.data.data:e.data?e.data:(console.warn("Empty response data"),{}),e=>{var t,r,a;return console.error("API Error:",e),(null==(t=e.response)?void 0:t.status)===401&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"),"/login"!==window.location.pathname&&(window.location.href="/login")),Promise.reject((null==(a=e.response)||null==(r=a.data)?void 0:r.error)||e.message||"An unknown error occurred")});let s={auth:{login:e=>a.post("/login",e),register:e=>a.post("/register",e),getProfile:()=>a.get("/me"),logout:()=>a.post("/logout")},categories:{getAll:e=>a.get("/categories",{params:{type:e}}),getById:e=>a.get("/categories/".concat(e)),create:e=>a.post("/categories",e),update:(e,t)=>a.put("/categories/".concat(e),t),delete:e=>a.delete("/categories/".concat(e))},accounts:{getAll:()=>a.get("/accounts"),getById:e=>a.get("/accounts/".concat(e)),create:e=>a.post("/accounts",e),update:(e,t)=>a.put("/accounts/".concat(e),t),delete:e=>a.delete("/accounts/".concat(e))},transactions:{getAll:e=>a.get("/transactions",{params:e}),getById:e=>a.get("/transactions/".concat(e)),create:e=>a.post("/transactions",e),update:(e,t)=>a.put("/transactions/".concat(e),t),delete:e=>a.delete("/transactions/".concat(e))},reports:{getSummary:async e=>{try{let t=await a.get("/reports/summary",{params:e});if(!t||"object"!=typeof t)return console.warn("Invalid summary response, using default values"),{totalIncome:0,totalExpense:0,netBalance:0,topExpenseCategories:[],topIncomeCategories:[]};return t}catch(e){return console.error("Error fetching summary:",e),{totalIncome:0,totalExpense:0,netBalance:0,topExpenseCategories:[],topIncomeCategories:[]}}},getCategoryBreakdown:async e=>{try{let t=await a.get("/reports/category-breakdown",{params:e});if(!t||"object"!=typeof t)return{expenseCategories:[],incomeCategories:[]};return t}catch(e){return console.error("Error fetching category breakdown:",e),{expenseCategories:[],incomeCategories:[]}}},getMonthlyReport:async e=>{try{let t=await a.get("/reports/monthly",{params:e});if(!t||"object"!=typeof t)return{months:[]};return t}catch(e){return console.error("Error fetching monthly report:",e),{months:[]}}},getLocationSummary:async e=>{try{let t=await a.get("/reports/location-summary",{params:e});if(!t||"object"!=typeof t)return{locations:[]};return t}catch(e){return console.error("Error fetching location summary:",e),{locations:[]}}}},bankStatements:{upload:e=>a.post("/bank-statements/upload",e,{headers:{"Content-Type":"multipart/form-data"}}),import:e=>a.post("/bank-statements/import",e)}}},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>c});var a=r(95155);r(12115);var s=r(99708),n=r(74466),o=r(59434);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:t,variant:r,size:n,asChild:c=!1,...l}=e,d=c?s.DX:"button";return(0,a.jsx)(d,{"data-slot":"button",className:(0,o.cn)(i({variant:r,size:n,className:t})),...l})}},59434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var a=r(52596),s=r(39688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var a=r(95155);r(12115);var s=r(59434);function n(e){let{className:t,type:r,...n}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},66695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>l,ZB:()=>i,Zp:()=>n,aR:()=>o,wL:()=>d});var a=r(95155);r(12115);var s=r(59434);function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",t),...r})}},85057:(e,t,r)=>{"use strict";r.d(t,{J:()=>o});var a=r(95155);r(12115);var s=r(40968),n=r(59434);function o(e){let{className:t,...r}=e;return(0,a.jsx)(s.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},93475:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>A});var a=r(95155),s=r(12115),n=r(30285),o=r(66695),i=r(62523),c=r(85057),l=r(76981),d=r(5196),u=r(59434);let p=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(l.bL,{ref:t,className:(0,u.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",r),...s,children:(0,a.jsx)(l.C1,{className:(0,u.cn)("flex items-center justify-center text-current"),children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})})});p.displayName=l.bL.displayName;var m=r(74466);let g=(0,m.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function f(e){let{className:t,variant:r,...s}=e;return(0,a.jsx)("div",{className:(0,u.cn)(g({variant:r}),t),...s})}let x=(0,m.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),h=s.forwardRef((e,t)=>{let{className:r,variant:s,...n}=e;return(0,a.jsx)("div",{ref:t,role:"alert",className:(0,u.cn)(x({variant:s}),r),...n})});h.displayName="Alert",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("h5",{ref:t,className:(0,u.cn)("mb-1 font-medium leading-none tracking-tight",r),...s})}).displayName="AlertTitle";let v=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,u.cn)("text-sm [&_p]:leading-relaxed",r),...s})});v.displayName="AlertDescription";var b=r(29869),y=r(54861),j=r(51154),w=r(57434),N=r(91788),k=r(25731),S=r(56671);function A(){let[e,t]=(0,s.useState)(!1),[r,l]=(0,s.useState)(!1),[d,u]=(0,s.useState)([]),[m,g]=(0,s.useState)([]),[x,A]=(0,s.useState)([]),[C,F]=(0,s.useState)(""),_=(0,s.useRef)(null);(0,s.useEffect)(()=>{P()},[]);let P=async()=>{try{let[e,t]=await Promise.all([k.A.categories.getAll(),k.A.accounts.getAll()]);g(e||[]),A(t||[])}catch(e){console.error("Error loading categories and accounts:",e)}},R=async e=>{var r;let a=null==(r=e.target.files)?void 0:r[0];if(a){if(!a.name.toLowerCase().endsWith(".pdf"))return void F("Please select a PDF file");t(!0),F("");try{let e=new FormData;e.append("file",a);let t=await k.A.bankStatements.upload(e);if(t.entries&&t.entries.length>0){let e=t.entries.map(e=>({...e,isSelected:!0}));u(e),S.oR.success("Successfully parsed ".concat(t.entries.length," transactions from the PDF"))}else F("No transactions found in the PDF file")}catch(e){console.error("Upload error:",e),F("string"==typeof e?e:"Failed to upload and parse the PDF file"),S.oR.error("Failed to upload bank statement")}finally{t(!1),_.current&&(_.current.value="")}}},E=e=>{u(t=>t.map((t,r)=>r===e?{...t,isSelected:!t.isSelected}:t))},B=async()=>{let e=d.filter(e=>e.isSelected);if(0===e.length)return void S.oR.error("Please select at least one transaction to import");l(!0),F("");try{let t=e.map(e=>({date:e.date,description:e.description,amount:e.amount,type:e.type,category_id:e.category_id,account_id:e.account_id}));await k.A.bankStatements.import(t),S.oR.success("Successfully imported ".concat(e.length," transactions")),u([])}catch(e){console.error("Import error:",e),F("string"==typeof e?e:"Failed to import transactions"),S.oR.error("Failed to import transactions")}finally{l(!1)}},I=e=>{if(!e)return"Auto-assigned";let t=m.find(t=>t.id===e);return t?t.name:"Unknown Category"},D=e=>{if(!e)return"Auto-assigned";let t=x.find(t=>t.id===e);return t?t.name:"Unknown Account"},T=d.filter(e=>e.isSelected).length,L=d.filter(e=>e.isSelected).reduce((e,t)=>e+("expense"===t.type?-t.amount:t.amount),0);return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Bank Statements"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Upload your bank statement PDF to automatically extract and import transactions"})]}),(0,a.jsxs)(o.Zp,{children:[(0,a.jsxs)(o.aR,{children:[(0,a.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(b.A,{className:"h-5 w-5"}),"Upload Bank Statement"]}),(0,a.jsx)(o.BT,{children:"Select a PDF file of your bank statement to extract transactions automatically"})]}),(0,a.jsxs)(o.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid w-full max-w-sm items-center gap-1.5",children:[(0,a.jsx)(c.J,{htmlFor:"pdf-file",children:"PDF File"}),(0,a.jsx)(i.p,{ref:_,id:"pdf-file",type:"file",accept:".pdf",onChange:R,disabled:e})]}),C&&(0,a.jsxs)(h,{variant:"destructive",children:[(0,a.jsx)(y.A,{className:"h-4 w-4"}),(0,a.jsx)(v,{children:C})]}),e&&(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 animate-spin"}),"Uploading and parsing PDF..."]})]})]}),d.length>0&&(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(w.A,{className:"h-5 w-5"}),"Extracted Transactions"]}),(0,a.jsxs)(o.BT,{children:["Review and select transactions to import (",T," of ",d.length," selected)"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>{let e=d.every(e=>e.isSelected);u(t=>t.map(t=>({...t,isSelected:!e})))},children:d.every(e=>e.isSelected)?"Deselect All":"Select All"}),(0,a.jsxs)(n.$,{onClick:B,disabled:0===T||r,className:"flex items-center gap-2",children:[r?(0,a.jsx)(j.A,{className:"h-4 w-4 animate-spin"}):(0,a.jsx)(N.A,{className:"h-4 w-4"}),"Import Selected (",T,")"]})]})]})}),(0,a.jsxs)(o.Wu,{children:[T>0&&(0,a.jsxs)("div",{className:"mb-4 p-3 bg-muted rounded-lg",children:[(0,a.jsxs)("div",{className:"text-sm font-medium",children:["Selected: ",T," transactions"]}),(0,a.jsxs)("div",{className:"text-sm ".concat(L>=0?"text-green-600":"text-red-600"),children:["Total: ",L>=0?"+":"",L.toFixed(2)," TL"]})]}),(0,a.jsx)("div",{className:"space-y-2 max-h-96 overflow-y-auto",children:d.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-3 border rounded-lg ".concat(e.isSelected?"bg-muted/50 border-primary":"bg-background"),children:[(0,a.jsx)(p,{checked:e.isSelected,onCheckedChange:()=>E(t)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("p",{className:"text-sm font-medium truncate",children:e.description}),(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsxs)(f,{variant:"expense"===e.type?"destructive":"default",children:["expense"===e.type?"-":"+",e.amount.toFixed(2)," TL"]})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4 mt-1 text-xs text-muted-foreground",children:[(0,a.jsx)("span",{children:e.date}),(0,a.jsxs)("span",{children:["Category: ",I(e.category_id)]}),(0,a.jsxs)("span",{children:["Account: ",D(e.account_id)]})]})]})]},t))})]})]})]})}},98250:(e,t,r)=>{Promise.resolve().then(r.bind(r,93475))}},e=>{var t=t=>e(e.s=t);e.O(0,[464,204,671,887,441,684,358],()=>t(98250)),_N_E=e.O()}]);