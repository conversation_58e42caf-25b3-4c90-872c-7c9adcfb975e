(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[337],{25731:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let s=a(23464).A.create({baseURL:"http://localhost:8008/api/v1",headers:{"Content-Type":"application/json"}});s.interceptors.request.use(e=>{{let t=localStorage.getItem("auth_token");t&&e.headers&&(e.headers.Authorization="Bearer ".concat(t))}return e},e=>Promise.reject(e)),s.interceptors.response.use(e=>(console.log("API Response:",e),e.data&&e.data.data)?e.data.data:e.data?e.data:(console.warn("Empty response data"),{}),e=>{var t,a,s;return console.error("API Error:",e),(null==(t=e.response)?void 0:t.status)===401&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"),"/login"!==window.location.pathname&&(window.location.href="/login")),Promise.reject((null==(s=e.response)||null==(a=s.data)?void 0:a.error)||e.message||"An unknown error occurred")});let n={auth:{login:e=>s.post("/login",e),register:e=>s.post("/register",e),getProfile:()=>s.get("/me"),logout:()=>s.post("/logout")},categories:{getAll:e=>s.get("/categories",{params:{type:e}}),getById:e=>s.get("/categories/".concat(e)),create:e=>s.post("/categories",e),update:(e,t)=>s.put("/categories/".concat(e),t),delete:e=>s.delete("/categories/".concat(e))},accounts:{getAll:()=>s.get("/accounts"),getById:e=>s.get("/accounts/".concat(e)),create:e=>s.post("/accounts",e),update:(e,t)=>s.put("/accounts/".concat(e),t),delete:e=>s.delete("/accounts/".concat(e))},transactions:{getAll:e=>s.get("/transactions",{params:e}),getById:e=>s.get("/transactions/".concat(e)),create:e=>s.post("/transactions",e),update:(e,t)=>s.put("/transactions/".concat(e),t),delete:e=>s.delete("/transactions/".concat(e))},reports:{getSummary:async e=>{try{let t=await s.get("/reports/summary",{params:e});if(!t||"object"!=typeof t)return console.warn("Invalid summary response, using default values"),{totalIncome:0,totalExpense:0,netBalance:0,topExpenseCategories:[],topIncomeCategories:[]};return t.data||t}catch(e){return console.error("Error fetching summary:",e),{totalIncome:0,totalExpense:0,netBalance:0,topExpenseCategories:[],topIncomeCategories:[]}}},getCategoryBreakdown:async e=>{try{let t=await s.get("/reports/category-breakdown",{params:e});if(!t||"object"!=typeof t)return{expenseCategories:[],incomeCategories:[]};return t.data||t}catch(e){return console.error("Error fetching category breakdown:",e),{expenseCategories:[],incomeCategories:[]}}},getMonthlyReport:async e=>{try{let t=await s.get("/reports/monthly",{params:e});if(!t||"object"!=typeof t)return{months:[]};return t.data||t}catch(e){return console.error("Error fetching monthly report:",e),{months:[]}}},getLocationSummary:async e=>{try{let t=await s.get("/reports/location-summary",{params:e});if(!t||"object"!=typeof t)return{locations:[]};return t.data||t}catch(e){return console.error("Error fetching location summary:",e),{locations:[]}}}},bankStatements:{upload:e=>s.post("/bank-statements/upload",e,{headers:{"Content-Type":"multipart/form-data"}}),import:e=>s.post("/bank-statements/import",e)}}},59434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>r});var s=a(52596),n=a(39688);function r(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,n.QP)((0,s.$)(t))}},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>c,Wu:()=>i,ZB:()=>l,Zp:()=>r,aR:()=>o,wL:()=>d});var s=a(95155);a(12115);var n=a(59434);function r(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...a})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},67891:(e,t,a)=>{Promise.resolve().then(a.bind(a,71628))},71628:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>_});var s=a(95155),n=a(12115),r=a(66695),o=a(60704),l=a(59434);function c(e){let{className:t,...a}=e;return(0,s.jsx)(o.bL,{"data-slot":"tabs",className:(0,l.cn)("flex flex-col gap-2",t),...a})}function i(e){let{className:t,...a}=e;return(0,s.jsx)(o.B8,{"data-slot":"tabs-list",className:(0,l.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsx)(o.l9,{"data-slot":"tabs-trigger",className:(0,l.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...a})}var u=a(33109),m=a(68500),x=a(39785),p=a(55868),g=a(13319),h=a(83540),f=a(3401),j=a(94754),y=a(96025),v=a(16238),b=a(94517),w=a(24026),N=a(83394),E=a(8782),I=a(34e3),B=a(54811),C=a(25731);let k=["#0088FE","#00C49F","#FFBB28","#FF8042","#8884D8","#82CA9D","#FFC658","#8DD1E1"];function _(){var e,t;let[a,o]=(0,n.useState)(null),[l,_]=(0,n.useState)([]),[A,F]=(0,n.useState)(!0),[S,M]=(0,n.useState)("month"),Z=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),R=()=>{let e=new Date,t=new Date;return"month"===S?t.setMonth(e.getMonth()-1):"quarter"===S?t.setMonth(e.getMonth()-3):"year"===S&&t.setFullYear(e.getFullYear()-1),{start_date:(0,g.GP)(t,"yyyy-MM-dd"),end_date:(0,g.GP)(e,"yyyy-MM-dd")}};(0,n.useEffect)(()=>{(async()=>{F(!0);try{let e=R(),t=await C.A.reports.getSummary(e);console.log("Summary data:",t);let a={totalIncome:(null==t?void 0:t.total_income)||(null==t?void 0:t.totalIncome)||0,totalExpense:(null==t?void 0:t.total_expense)||(null==t?void 0:t.totalExpense)||0,netBalance:(null==t?void 0:t.net_balance)||(null==t?void 0:t.netBalance)||0,topExpenseCategories:(null==t?void 0:t.top_expense_categories)||(null==t?void 0:t.topExpenseCategories)||[],topIncomeCategories:(null==t?void 0:t.top_income_categories)||(null==t?void 0:t.topIncomeCategories)||[]};o(a);let s=await C.A.reports.getMonthlyReport({year:new Date().getFullYear()});console.log("Monthly data:",s),_((null==s?void 0:s.months)||[])}catch(e){console.error("Error fetching dashboard data:",e),o({totalIncome:0,totalExpense:0,netBalance:0,topExpenseCategories:[],topIncomeCategories:[]}),_([])}finally{F(!1)}})()},[S]);let D=(null==a||null==(e=a.topExpenseCategories)?void 0:e.map(e=>({name:e.category,value:e.amount})))||[];return null==a||null==(t=a.topIncomeCategories)||t.map(e=>({name:e.category,value:e.amount})),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-3xl font-bold tracking-tight",children:"Dashboard"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Overview of your financial activities"})]}),(0,s.jsx)(c,{defaultValue:"month",className:"w-full md:w-auto",onValueChange:M,children:(0,s.jsxs)(i,{children:[(0,s.jsx)(d,{value:"month",children:"Month"}),(0,s.jsx)(d,{value:"quarter",children:"Quarter"}),(0,s.jsx)(d,{value:"year",children:"Year"})]})})]}),A?(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary"})}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,s.jsxs)(r.Zp,{children:[(0,s.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(r.ZB,{className:"text-sm font-medium",children:"Total Income"}),(0,s.jsx)(u.A,{className:"h-4 w-4 text-green-500"})]}),(0,s.jsxs)(r.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold",children:Z((null==a?void 0:a.totalIncome)||0)}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"For the selected period"})]})]}),(0,s.jsxs)(r.Zp,{children:[(0,s.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(r.ZB,{className:"text-sm font-medium",children:"Total Expenses"}),(0,s.jsx)(m.A,{className:"h-4 w-4 text-red-500"})]}),(0,s.jsxs)(r.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold",children:Z((null==a?void 0:a.totalExpense)||0)}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"For the selected period"})]})]}),(0,s.jsxs)(r.Zp,{children:[(0,s.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(r.ZB,{className:"text-sm font-medium",children:"Net Balance"}),(0,s.jsx)(x.A,{className:"h-4 w-4 text-blue-500"})]}),(0,s.jsxs)(r.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold",children:Z((null==a?void 0:a.netBalance)||0)}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Income minus expenses"})]})]}),(0,s.jsxs)(r.Zp,{children:[(0,s.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(r.ZB,{className:"text-sm font-medium",children:"Savings Rate"}),(0,s.jsx)(p.A,{className:"h-4 w-4 text-yellow-500"})]}),(0,s.jsxs)(r.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold",children:(null==a?void 0:a.totalIncome)&&a.totalIncome>0?"".concat(Math.round(a.netBalance/a.totalIncome*100),"%"):"0%"}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Net balance / Total income"})]})]})]}),(0,s.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,s.jsxs)(r.Zp,{className:"col-span-2 md:col-span-1",children:[(0,s.jsxs)(r.aR,{children:[(0,s.jsx)(r.ZB,{children:"Monthly Income vs Expenses"}),(0,s.jsx)(r.BT,{children:"Comparison of income and expenses over the past months"})]}),(0,s.jsx)(r.Wu,{className:"h-80",children:(0,s.jsx)(h.u,{width:"100%",height:"100%",children:(0,s.jsxs)(f.E,{data:l,margin:{top:5,right:30,left:20,bottom:5},children:[(0,s.jsx)(j.d,{strokeDasharray:"3 3"}),(0,s.jsx)(y.W,{dataKey:"month"}),(0,s.jsx)(v.h,{}),(0,s.jsx)(b.m,{formatter:e=>Z(Number(e))}),(0,s.jsx)(w.s,{}),(0,s.jsx)(N.y,{dataKey:"income",fill:"#4ade80",name:"Income"}),(0,s.jsx)(N.y,{dataKey:"expense",fill:"#f87171",name:"Expense"})]})})})]}),(0,s.jsxs)(r.Zp,{className:"col-span-2 md:col-span-1",children:[(0,s.jsxs)(r.aR,{children:[(0,s.jsx)(r.ZB,{children:"Expense Categories"}),(0,s.jsx)(r.BT,{children:"Breakdown of expenses by category"})]}),(0,s.jsx)(r.Wu,{className:"h-80",children:(0,s.jsx)(h.u,{width:"100%",height:"100%",children:(0,s.jsxs)(E.r,{children:[(0,s.jsx)(I.F,{data:D,cx:"50%",cy:"50%",labelLine:!1,label:e=>{let{name:t,percent:a}=e;return"".concat(t,": ").concat((100*a).toFixed(0),"%")},outerRadius:80,fill:"#8884d8",dataKey:"value",children:D.map((e,t)=>(0,s.jsx)(B.f,{fill:k[t%k.length]},"cell-".concat(t)))}),(0,s.jsx)(b.m,{formatter:e=>Z(Number(e))}),(0,s.jsx)(w.s,{})]})})})]})]})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[464,204,550,319,927,441,684,358],()=>t(67891)),_N_E=e.O()}]);