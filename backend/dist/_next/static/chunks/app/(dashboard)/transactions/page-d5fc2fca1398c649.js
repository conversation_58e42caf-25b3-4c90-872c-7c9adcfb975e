(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[486],{10814:(e,t,a)=>{Promise.resolve().then(a.bind(a,85590))},15452:(e,t,a)=>{"use strict";a.d(t,{UC:()=>ea,VY:()=>en,ZL:()=>ee,bL:()=>K,bm:()=>es,hE:()=>er,hJ:()=>et,l9:()=>Q});var r=a(12115),n=a(85185),s=a(6101),o=a(46081),l=a(61285),i=a(5845),c=a(19178),d=a(25519),u=a(34378),x=a(28905),p=a(63655),m=a(92293),h=a(93795),g=a(38168),f=a(99708),j=a(95155),v="Dialog",[y,b]=(0,o.A)(v),[w,C]=y(v),N=e=>{let{__scopeDialog:t,children:a,open:n,defaultOpen:s,onOpenChange:o,modal:c=!0}=e,d=r.useRef(null),u=r.useRef(null),[x,p]=(0,i.i)({prop:n,defaultProp:null!=s&&s,onChange:o,caller:v});return(0,j.jsx)(w,{scope:t,triggerRef:d,contentRef:u,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:x,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:c,children:a})};N.displayName=v;var k="DialogTrigger",_=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,o=C(k,a),l=(0,s.s)(t,o.triggerRef);return(0,j.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":Z(o.open),...r,ref:l,onClick:(0,n.m)(e.onClick,o.onOpenToggle)})});_.displayName=k;var A="DialogPortal",[z,I]=y(A,{forceMount:void 0}),R=e=>{let{__scopeDialog:t,forceMount:a,children:n,container:s}=e,o=C(A,t);return(0,j.jsx)(z,{scope:t,forceMount:a,children:r.Children.map(n,e=>(0,j.jsx)(x.C,{present:a||o.open,children:(0,j.jsx)(u.Z,{asChild:!0,container:s,children:e})}))})};R.displayName=A;var D="DialogOverlay",M=r.forwardRef((e,t)=>{let a=I(D,e.__scopeDialog),{forceMount:r=a.forceMount,...n}=e,s=C(D,e.__scopeDialog);return s.modal?(0,j.jsx)(x.C,{present:r||s.open,children:(0,j.jsx)(S,{...n,ref:t})}):null});M.displayName=D;var E=(0,f.TL)("DialogOverlay.RemoveScroll"),S=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,n=C(D,a);return(0,j.jsx)(h.A,{as:E,allowPinchZoom:!0,shards:[n.contentRef],children:(0,j.jsx)(p.sG.div,{"data-state":Z(n.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),B="DialogContent",T=r.forwardRef((e,t)=>{let a=I(B,e.__scopeDialog),{forceMount:r=a.forceMount,...n}=e,s=C(B,e.__scopeDialog);return(0,j.jsx)(x.C,{present:r||s.open,children:s.modal?(0,j.jsx)(F,{...n,ref:t}):(0,j.jsx)(P,{...n,ref:t})})});T.displayName=B;var F=r.forwardRef((e,t)=>{let a=C(B,e.__scopeDialog),o=r.useRef(null),l=(0,s.s)(t,a.contentRef,o);return r.useEffect(()=>{let e=o.current;if(e)return(0,g.Eq)(e)},[]),(0,j.jsx)(V,{...e,ref:l,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,n.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=a.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,n.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,a=0===t.button&&!0===t.ctrlKey;(2===t.button||a)&&e.preventDefault()}),onFocusOutside:(0,n.m)(e.onFocusOutside,e=>e.preventDefault())})}),P=r.forwardRef((e,t)=>{let a=C(B,e.__scopeDialog),n=r.useRef(!1),s=r.useRef(!1);return(0,j.jsx)(V,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,o;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(n.current||null==(o=a.triggerRef.current)||o.focus(),t.preventDefault()),n.current=!1,s.current=!1},onInteractOutside:t=>{var r,o;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(n.current=!0,"pointerdown"===t.detail.originalEvent.type&&(s.current=!0));let l=t.target;(null==(o=a.triggerRef.current)?void 0:o.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&s.current&&t.preventDefault()}})}),V=r.forwardRef((e,t)=>{let{__scopeDialog:a,trapFocus:n,onOpenAutoFocus:o,onCloseAutoFocus:l,...i}=e,u=C(B,a),x=r.useRef(null),p=(0,s.s)(t,x);return(0,m.Oh)(),(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)(d.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:o,onUnmountAutoFocus:l,children:(0,j.jsx)(c.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":Z(u.open),...i,ref:p,onDismiss:()=>u.onOpenChange(!1)})}),(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)(H,{titleId:u.titleId}),(0,j.jsx)(X,{contentRef:x,descriptionId:u.descriptionId})]})]})}),q="DialogTitle",O=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,n=C(q,a);return(0,j.jsx)(p.sG.h2,{id:n.titleId,...r,ref:t})});O.displayName=q;var L="DialogDescription",J=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,n=C(L,a);return(0,j.jsx)(p.sG.p,{id:n.descriptionId,...r,ref:t})});J.displayName=L;var U="DialogClose",G=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,s=C(U,a);return(0,j.jsx)(p.sG.button,{type:"button",...r,ref:t,onClick:(0,n.m)(e.onClick,()=>s.onOpenChange(!1))})});function Z(e){return e?"open":"closed"}G.displayName=U;var W="DialogTitleWarning",[$,Y]=(0,o.q)(W,{contentName:B,titleName:q,docsSlug:"dialog"}),H=e=>{let{titleId:t}=e,a=Y(W),n="`".concat(a.contentName,"` requires a `").concat(a.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(a.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(a.docsSlug);return r.useEffect(()=>{t&&(document.getElementById(t)||console.error(n))},[n,t]),null},X=e=>{let{contentRef:t,descriptionId:a}=e,n=Y("DialogDescriptionWarning"),s="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(n.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");a&&r&&(document.getElementById(a)||console.warn(s))},[s,t,a]),null},K=N,Q=_,ee=R,et=M,ea=T,er=O,en=J,es=G},17759:(e,t,a)=>{"use strict";a.d(t,{C5:()=>f,MJ:()=>g,eI:()=>m,lR:()=>h,lV:()=>c,zB:()=>u});var r=a(95155),n=a(12115),s=a(99708),o=a(62177),l=a(59434),i=a(85057);let c=o.Op,d=n.createContext({}),u=e=>{let{...t}=e;return(0,r.jsx)(d.Provider,{value:{name:t.name},children:(0,r.jsx)(o.xI,{...t})})},x=()=>{let e=n.useContext(d),t=n.useContext(p),{getFieldState:a}=(0,o.xW)(),r=(0,o.lN)({name:e.name}),s=a(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:"".concat(l,"-form-item"),formDescriptionId:"".concat(l,"-form-item-description"),formMessageId:"".concat(l,"-form-item-message"),...s}},p=n.createContext({});function m(e){let{className:t,...a}=e,s=n.useId();return(0,r.jsx)(p.Provider,{value:{id:s},children:(0,r.jsx)("div",{"data-slot":"form-item",className:(0,l.cn)("grid gap-2",t),...a})})}function h(e){let{className:t,...a}=e,{error:n,formItemId:s}=x();return(0,r.jsx)(i.J,{"data-slot":"form-label","data-error":!!n,className:(0,l.cn)("data-[error=true]:text-destructive",t),htmlFor:s,...a})}function g(e){let{...t}=e,{error:a,formItemId:n,formDescriptionId:o,formMessageId:l}=x();return(0,r.jsx)(s.DX,{"data-slot":"form-control",id:n,"aria-describedby":a?"".concat(o," ").concat(l):"".concat(o),"aria-invalid":!!a,...t})}function f(e){var t;let{className:a,...n}=e,{error:s,formMessageId:o}=x(),i=s?String(null!=(t=null==s?void 0:s.message)?t:""):n.children;return i?(0,r.jsx)("p",{"data-slot":"form-message",id:o,className:(0,l.cn)("text-destructive text-sm",a),...n,children:i}):null}},25731:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let r=a(23464).A.create({baseURL:"http://localhost:8008/api/v1",headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{{let t=localStorage.getItem("auth_token");t&&e.headers&&(e.headers.Authorization="Bearer ".concat(t))}return e},e=>Promise.reject(e)),r.interceptors.response.use(e=>(console.log("API Response:",e),e.data&&e.data.data)?e.data.data:e.data?e.data:(console.warn("Empty response data"),{}),e=>{var t,a,r;return console.error("API Error:",e),(null==(t=e.response)?void 0:t.status)===401&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"),"/login"!==window.location.pathname&&(window.location.href="/login")),Promise.reject((null==(r=e.response)||null==(a=r.data)?void 0:a.error)||e.message||"An unknown error occurred")});let n={auth:{login:e=>r.post("/login",e),register:e=>r.post("/register",e),getProfile:()=>r.get("/me"),logout:()=>r.post("/logout")},categories:{getAll:e=>r.get("/categories",{params:{type:e}}),getById:e=>r.get("/categories/".concat(e)),create:e=>r.post("/categories",e),update:(e,t)=>r.put("/categories/".concat(e),t),delete:e=>r.delete("/categories/".concat(e))},accounts:{getAll:()=>r.get("/accounts"),getById:e=>r.get("/accounts/".concat(e)),create:e=>r.post("/accounts",e),update:(e,t)=>r.put("/accounts/".concat(e),t),delete:e=>r.delete("/accounts/".concat(e))},transactions:{getAll:e=>r.get("/transactions",{params:e}),getById:e=>r.get("/transactions/".concat(e)),create:e=>r.post("/transactions",e),update:(e,t)=>r.put("/transactions/".concat(e),t),delete:e=>r.delete("/transactions/".concat(e))},reports:{getSummary:async e=>{try{let t=await r.get("/reports/summary",{params:e});if(!t||"object"!=typeof t)return console.warn("Invalid summary response, using default values"),{totalIncome:0,totalExpense:0,netBalance:0,topExpenseCategories:[],topIncomeCategories:[]};return t}catch(e){return console.error("Error fetching summary:",e),{totalIncome:0,totalExpense:0,netBalance:0,topExpenseCategories:[],topIncomeCategories:[]}}},getCategoryBreakdown:async e=>{try{let t=await r.get("/reports/category-breakdown",{params:e});if(!t||"object"!=typeof t)return{expenseCategories:[],incomeCategories:[]};return t}catch(e){return console.error("Error fetching category breakdown:",e),{expenseCategories:[],incomeCategories:[]}}},getMonthlyReport:async e=>{try{let t=await r.get("/reports/monthly",{params:e});if(!t||"object"!=typeof t)return{months:[]};return t}catch(e){return console.error("Error fetching monthly report:",e),{months:[]}}},getLocationSummary:async e=>{try{let t=await r.get("/reports/location-summary",{params:e});if(!t||"object"!=typeof t)return{locations:[]};return t}catch(e){return console.error("Error fetching location summary:",e),{locations:[]}}}},bankStatements:{upload:e=>r.post("/bank-statements/upload",e,{headers:{"Content-Type":"multipart/form-data"}}),import:e=>r.post("/bank-statements/import",e)}}},30285:(e,t,a)=>{"use strict";a.d(t,{$:()=>i});var r=a(95155);a(12115);var n=a(99708),s=a(74466),o=a(59434);let l=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:t,variant:a,size:s,asChild:i=!1,...c}=e,d=i?n.DX:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,o.cn)(l({variant:a,size:s,className:t})),...c})}},54416:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},59409:(e,t,a)=>{"use strict";a.d(t,{bq:()=>u,eb:()=>p,gC:()=>x,l6:()=>c,yv:()=>d});var r=a(95155);a(12115);var n=a(38715),s=a(66474),o=a(5196),l=a(47863),i=a(59434);function c(e){let{...t}=e;return(0,r.jsx)(n.bL,{"data-slot":"select",...t})}function d(e){let{...t}=e;return(0,r.jsx)(n.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:a="default",children:o,...l}=e;return(0,r.jsxs)(n.l9,{"data-slot":"select-trigger","data-size":a,className:(0,i.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l,children:[o,(0,r.jsx)(n.In,{asChild:!0,children:(0,r.jsx)(s.A,{className:"size-4 opacity-50"})})]})}function x(e){let{className:t,children:a,position:s="popper",...o}=e;return(0,r.jsx)(n.ZL,{children:(0,r.jsxs)(n.UC,{"data-slot":"select-content",className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:s,...o,children:[(0,r.jsx)(m,{}),(0,r.jsx)(n.LM,{className:(0,i.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,r.jsx)(h,{})]})})}function p(e){let{className:t,children:a,...s}=e;return(0,r.jsxs)(n.q7,{"data-slot":"select-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...s,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(n.VF,{children:(0,r.jsx)(o.A,{className:"size-4"})})}),(0,r.jsx)(n.p4,{children:a})]})}function m(e){let{className:t,...a}=e;return(0,r.jsx)(n.PP,{"data-slot":"select-scroll-up-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(l.A,{className:"size-4"})})}function h(e){let{className:t,...a}=e;return(0,r.jsx)(n.wn,{"data-slot":"select-scroll-down-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(s.A,{className:"size-4"})})}},59434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>s});var r=a(52596),n=a(39688);function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,n.QP)((0,r.$)(t))}},62523:(e,t,a)=>{"use strict";a.d(t,{p:()=>s});var r=a(95155);a(12115);var n=a(59434);function s(e){let{className:t,type:a,...s}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...s})}},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>i,Wu:()=>c,ZB:()=>l,Zp:()=>s,aR:()=>o,wL:()=>d});var r=a(95155);a(12115);var n=a(59434);function s(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...a})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},85057:(e,t,a)=>{"use strict";a.d(t,{J:()=>o});var r=a(95155);a(12115);var n=a(40968),s=a(59434);function o(e){let{className:t,...a}=e;return(0,r.jsx)(n.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},85590:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>G});var r=a(95155),n=a(12115),s=a(35695),o=a(13319),l=a(59434);function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,l.cn)("w-full caption-bottom text-sm",t),...a})})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,l.cn)("[&_tr]:border-b",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,l.cn)("[&_tr:last-child]:border-0",t),...a})}function u(e){let{className:t,...a}=e;return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,l.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...a})}function x(e){let{className:t,...a}=e;return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,l.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}function p(e){let{className:t,...a}=e;return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,l.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}var m=a(30285),h=a(62523),g=a(66695),f=a(15452),j=a(54416);function v(e){let{...t}=e;return(0,r.jsx)(f.bL,{"data-slot":"dialog",...t})}function y(e){let{...t}=e;return(0,r.jsx)(f.l9,{"data-slot":"dialog-trigger",...t})}function b(e){let{...t}=e;return(0,r.jsx)(f.ZL,{"data-slot":"dialog-portal",...t})}function w(e){let{className:t,...a}=e;return(0,r.jsx)(f.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function C(e){let{className:t,children:a,...n}=e;return(0,r.jsxs)(b,{"data-slot":"dialog-portal",children:[(0,r.jsx)(w,{}),(0,r.jsxs)(f.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...n,children:[a,(0,r.jsxs)(f.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(j.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function N(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function k(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function _(e){let{className:t,...a}=e;return(0,r.jsx)(f.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",t),...a})}function A(e){let{className:t,...a}=e;return(0,r.jsx)(f.VY,{"data-slot":"dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",t),...a})}var z=a(59409),I=a(17759);function R(e){let{className:t,...a}=e;return(0,r.jsx)("textarea",{"data-slot":"textarea",className:(0,l.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...a})}var D=a(90221),M=a(62177),E=a(71153),S=a(19946);let B=(0,S.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var T=a(51154);let F=(0,S.A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]),P=(0,S.A)("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]),V=(0,S.A)("arrow-down",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]),q=(0,S.A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),O=(0,S.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);var L=a(56671),J=a(25731);let U=E.z.object({title:E.z.string().min(1,"Title is required"),type:E.z.enum(["income","expense"]),amount:E.z.coerce.number().positive("Amount must be positive"),currency:E.z.string().min(1,"Currency is required"),category_id:E.z.string().min(1,"Category is required"),payment_method:E.z.string().min(1,"Payment method is required"),account_id:E.z.string().min(1,"Account is required"),note:E.z.string().optional(),transaction_date:E.z.string().min(1,"Date is required"),location:E.z.string().optional()});function G(){(0,s.useRouter)();let[e,t]=(0,n.useState)([]),[a,l]=(0,n.useState)([]),[f,j]=(0,n.useState)([]),[b,w]=(0,n.useState)(!0),[E,S]=(0,n.useState)(!1),[G,Z]=(0,n.useState)(!1),[W,$]=(0,n.useState)(null),[Y,H]=(0,n.useState)(""),[X,K]=(0,n.useState)("all"),Q=(0,M.mN)({resolver:(0,D.u)(U),defaultValues:{title:"",type:"expense",amount:0,currency:"USD",category_id:"",payment_method:"cash",account_id:"",note:"",transaction_date:(0,o.GP)(new Date,"yyyy-MM-dd"),location:""}});(0,n.useEffect)(()=>{(async()=>{w(!0);try{let e=await J.A.transactions.getAll();t(e);let a=await J.A.categories.getAll();l(a);let r=await J.A.accounts.getAll();j(r)}catch(e){console.error("Error fetching data:",e),L.oR.error("Failed to load data")}finally{w(!1)}})()},[]);let ee=async e=>{S(!0);try{W?(await J.A.transactions.update(W.id,e),L.oR.success("Transaction updated successfully")):(await J.A.transactions.create(e),L.oR.success("Transaction created successfully"));let a=await J.A.transactions.getAll();t(a),Z(!1),Q.reset(),$(null)}catch(e){console.error("Error saving transaction:",e),L.oR.error("string"==typeof e?e:"Failed to save transaction")}finally{S(!1)}},et=e=>{$(e),Q.reset({title:e.title,type:e.type,amount:e.amount,currency:e.currency,category_id:e.category_id,payment_method:e.payment_method,account_id:e.account_id,note:e.note||"",transaction_date:(0,o.GP)(new Date(e.transaction_date),"yyyy-MM-dd"),location:e.location||""}),Z(!0)},ea=async e=>{if(confirm("Are you sure you want to delete this transaction?"))try{await J.A.transactions.delete(e),L.oR.success("Transaction deleted successfully");let a=await J.A.transactions.getAll();t(a)}catch(e){console.error("Error deleting transaction:",e),L.oR.error("string"==typeof e?e:"Failed to delete transaction")}},er=(e,t)=>new Intl.NumberFormat("en-US",{style:"currency",currency:t}).format(e),en=e.filter(e=>{if("all"!==X&&e.type!==X)return!1;if(Y){var t;let a=Y.toLowerCase();return e.title.toLowerCase().includes(a)||(null==(t=e.note)?void 0:t.toLowerCase().includes(a))||e.category.name.toLowerCase().includes(a)||e.account.name.toLowerCase().includes(a)||e.payment_method.toLowerCase().includes(a)}return!0});return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-3xl font-bold tracking-tight",children:"Transactions"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Manage your income and expenses"})]}),(0,r.jsxs)(v,{open:G,onOpenChange:Z,children:[(0,r.jsx)(y,{asChild:!0,children:(0,r.jsxs)(m.$,{children:[(0,r.jsx)(B,{className:"mr-2 h-4 w-4"}),"Add Transaction"]})}),(0,r.jsxs)(C,{className:"sm:max-w-[600px]",children:[(0,r.jsxs)(N,{children:[(0,r.jsx)(_,{children:W?"Edit Transaction":"Add New Transaction"}),(0,r.jsx)(A,{children:W?"Update the transaction details below":"Fill in the details to add a new transaction"})]}),(0,r.jsx)(I.lV,{...Q,children:(0,r.jsxs)("form",{onSubmit:Q.handleSubmit(ee),className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(I.zB,{control:Q.control,name:"title",render:e=>{let{field:t}=e;return(0,r.jsxs)(I.eI,{children:[(0,r.jsx)(I.lR,{children:"Title"}),(0,r.jsx)(I.MJ,{children:(0,r.jsx)(h.p,{placeholder:"Transaction title",...t})}),(0,r.jsx)(I.C5,{})]})}}),(0,r.jsx)(I.zB,{control:Q.control,name:"type",render:e=>{let{field:t}=e;return(0,r.jsxs)(I.eI,{children:[(0,r.jsx)(I.lR,{children:"Type"}),(0,r.jsxs)(z.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,r.jsx)(I.MJ,{children:(0,r.jsx)(z.bq,{children:(0,r.jsx)(z.yv,{placeholder:"Select type"})})}),(0,r.jsxs)(z.gC,{children:[(0,r.jsx)(z.eb,{value:"expense",children:"Expense"}),(0,r.jsx)(z.eb,{value:"income",children:"Income"})]})]}),(0,r.jsx)(I.C5,{})]})}}),(0,r.jsx)(I.zB,{control:Q.control,name:"amount",render:e=>{let{field:t}=e;return(0,r.jsxs)(I.eI,{children:[(0,r.jsx)(I.lR,{children:"Amount"}),(0,r.jsx)(I.MJ,{children:(0,r.jsx)(h.p,{type:"number",placeholder:"0.00",...t,onChange:e=>t.onChange(parseFloat(e.target.value))})}),(0,r.jsx)(I.C5,{})]})}}),(0,r.jsx)(I.zB,{control:Q.control,name:"currency",render:e=>{let{field:t}=e;return(0,r.jsxs)(I.eI,{children:[(0,r.jsx)(I.lR,{children:"Currency"}),(0,r.jsxs)(z.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,r.jsx)(I.MJ,{children:(0,r.jsx)(z.bq,{children:(0,r.jsx)(z.yv,{placeholder:"Select currency"})})}),(0,r.jsxs)(z.gC,{children:[(0,r.jsx)(z.eb,{value:"USD",children:"USD"}),(0,r.jsx)(z.eb,{value:"EUR",children:"EUR"}),(0,r.jsx)(z.eb,{value:"GBP",children:"GBP"}),(0,r.jsx)(z.eb,{value:"TRY",children:"TRY"})]})]}),(0,r.jsx)(I.C5,{})]})}}),(0,r.jsx)(I.zB,{control:Q.control,name:"category_id",render:e=>{let{field:t}=e;return(0,r.jsxs)(I.eI,{children:[(0,r.jsx)(I.lR,{children:"Category"}),(0,r.jsxs)(z.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,r.jsx)(I.MJ,{children:(0,r.jsx)(z.bq,{children:(0,r.jsx)(z.yv,{placeholder:"Select category"})})}),(0,r.jsx)(z.gC,{children:a.filter(e=>e.type===Q.getValues("type")).map(e=>(0,r.jsxs)(z.eb,{value:e.id,children:[e.icon," ",e.name]},e.id))})]}),(0,r.jsx)(I.C5,{})]})}}),(0,r.jsx)(I.zB,{control:Q.control,name:"account_id",render:e=>{let{field:t}=e;return(0,r.jsxs)(I.eI,{children:[(0,r.jsx)(I.lR,{children:"Account"}),(0,r.jsxs)(z.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,r.jsx)(I.MJ,{children:(0,r.jsx)(z.bq,{children:(0,r.jsx)(z.yv,{placeholder:"Select account"})})}),(0,r.jsx)(z.gC,{children:f.map(e=>(0,r.jsx)(z.eb,{value:e.id,children:e.name},e.id))})]}),(0,r.jsx)(I.C5,{})]})}}),(0,r.jsx)(I.zB,{control:Q.control,name:"payment_method",render:e=>{let{field:t}=e;return(0,r.jsxs)(I.eI,{children:[(0,r.jsx)(I.lR,{children:"Payment Method"}),(0,r.jsxs)(z.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,r.jsx)(I.MJ,{children:(0,r.jsx)(z.bq,{children:(0,r.jsx)(z.yv,{placeholder:"Select payment method"})})}),(0,r.jsxs)(z.gC,{children:[(0,r.jsx)(z.eb,{value:"cash",children:"Cash"}),(0,r.jsx)(z.eb,{value:"credit_card",children:"Credit Card"}),(0,r.jsx)(z.eb,{value:"debit_card",children:"Debit Card"}),(0,r.jsx)(z.eb,{value:"bank_transfer",children:"Bank Transfer"}),(0,r.jsx)(z.eb,{value:"mobile_payment",children:"Mobile Payment"})]})]}),(0,r.jsx)(I.C5,{})]})}}),(0,r.jsx)(I.zB,{control:Q.control,name:"transaction_date",render:e=>{let{field:t}=e;return(0,r.jsxs)(I.eI,{children:[(0,r.jsx)(I.lR,{children:"Date"}),(0,r.jsx)(I.MJ,{children:(0,r.jsx)(h.p,{type:"date",...t})}),(0,r.jsx)(I.C5,{})]})}}),(0,r.jsx)(I.zB,{control:Q.control,name:"location",render:e=>{let{field:t}=e;return(0,r.jsxs)(I.eI,{children:[(0,r.jsx)(I.lR,{children:"Location (Optional)"}),(0,r.jsx)(I.MJ,{children:(0,r.jsx)(h.p,{placeholder:"Location",...t})}),(0,r.jsx)(I.C5,{})]})}})]}),(0,r.jsx)(I.zB,{control:Q.control,name:"note",render:e=>{let{field:t}=e;return(0,r.jsxs)(I.eI,{children:[(0,r.jsx)(I.lR,{children:"Note (Optional)"}),(0,r.jsx)(I.MJ,{children:(0,r.jsx)(R,{placeholder:"Add any additional notes",className:"resize-none",...t})}),(0,r.jsx)(I.C5,{})]})}}),(0,r.jsx)(k,{children:(0,r.jsx)(m.$,{type:"submit",disabled:E,children:E?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(T.A,{className:"mr-2 h-4 w-4 animate-spin"}),W?"Updating...":"Creating..."]}):W?"Update Transaction":"Create Transaction"})})]})})]})]})]}),(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(F,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,r.jsx)(h.p,{placeholder:"Search transactions...",className:"pl-9",value:Y,onChange:e=>H(e.target.value)})]}),(0,r.jsxs)(z.l6,{value:X,onValueChange:K,children:[(0,r.jsx)(z.bq,{className:"w-full md:w-[180px]",children:(0,r.jsx)(z.yv,{placeholder:"Filter by type"})}),(0,r.jsxs)(z.gC,{children:[(0,r.jsx)(z.eb,{value:"all",children:"All Types"}),(0,r.jsx)(z.eb,{value:"income",children:"Income"}),(0,r.jsx)(z.eb,{value:"expense",children:"Expense"})]})]})]}),(0,r.jsxs)(g.Zp,{children:[(0,r.jsx)(g.aR,{children:(0,r.jsx)(g.ZB,{children:"Recent Transactions"})}),(0,r.jsx)(g.Wu,{children:b?(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary"})}):0===en.length?(0,r.jsx)("div",{className:"text-center py-10",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"No transactions found"})}):(0,r.jsxs)(i,{children:[(0,r.jsx)(c,{children:(0,r.jsxs)(u,{children:[(0,r.jsx)(x,{children:"Date"}),(0,r.jsx)(x,{children:"Title"}),(0,r.jsx)(x,{children:"Category"}),(0,r.jsx)(x,{children:"Account"}),(0,r.jsx)(x,{className:"text-right",children:"Amount"}),(0,r.jsx)(x,{className:"text-right",children:"Actions"})]})}),(0,r.jsx)(d,{children:en.map(e=>(0,r.jsxs)(u,{children:[(0,r.jsx)(p,{children:(0,o.GP)(new Date(e.transaction_date),"MMM dd, yyyy")}),(0,r.jsx)(p,{children:e.title}),(0,r.jsx)(p,{children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{children:e.category.icon}),(0,r.jsx)("span",{children:e.category.name})]})}),(0,r.jsx)(p,{children:e.account.name}),(0,r.jsx)(p,{className:"text-right",children:(0,r.jsxs)("div",{className:"flex items-center justify-end gap-1 font-medium ".concat("income"===e.type?"text-green-600":"text-red-600"),children:["income"===e.type?(0,r.jsx)(P,{className:"h-4 w-4"}):(0,r.jsx)(V,{className:"h-4 w-4"}),er(e.amount,e.currency)]})}),(0,r.jsx)(p,{className:"text-right",children:(0,r.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,r.jsxs)(m.$,{variant:"ghost",size:"icon",onClick:()=>et(e),children:[(0,r.jsx)(q,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Edit"})]}),(0,r.jsxs)(m.$,{variant:"ghost",size:"icon",className:"text-red-600",onClick:()=>ea(e.id),children:[(0,r.jsx)(O,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Delete"})]})]})})]},e.id))})]})})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[464,204,671,550,268,433,319,171,441,684,358],()=>t(10814)),_N_E=e.O()}]);