(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[486],{10814:(e,t,a)=>{Promise.resolve().then(a.bind(a,89390))},25731:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let r=a(23464).A.create({baseURL:"https://app.kolay-butce.com/api/v1",headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{{let t=localStorage.getItem("auth_token");t&&e.headers&&(e.headers.Authorization="Bearer ".concat(t))}return e},e=>Promise.reject(e)),r.interceptors.response.use(e=>(console.log("API Response:",e),e.data&&e.data.data)?e.data.data:e.data?e.data:(console.warn("Empty response data"),{}),e=>{var t,a,r;return console.error("API Error:",e),(null==(t=e.response)?void 0:t.status)===401&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"),"/login"!==window.location.pathname&&(window.location.href="/login")),Promise.reject((null==(r=e.response)||null==(a=r.data)?void 0:a.error)||e.message||"An unknown error occurred")});let n={auth:{login:e=>r.post("/login",e),register:e=>r.post("/register",e),getProfile:()=>r.get("/me"),logout:()=>r.post("/logout")},categories:{getAll:e=>r.get("/categories",{params:{type:e}}),getById:e=>r.get("/categories/".concat(e)),create:e=>r.post("/categories",e),update:(e,t)=>r.put("/categories/".concat(e),t),delete:e=>r.delete("/categories/".concat(e))},accounts:{getAll:()=>r.get("/accounts"),getById:e=>r.get("/accounts/".concat(e)),create:e=>r.post("/accounts",e),update:(e,t)=>r.put("/accounts/".concat(e),t),delete:e=>r.delete("/accounts/".concat(e))},transactions:{getAll:e=>r.get("/transactions",{params:e}),getById:e=>r.get("/transactions/".concat(e)),create:e=>r.post("/transactions",e),update:(e,t)=>r.put("/transactions/".concat(e),t),delete:e=>r.delete("/transactions/".concat(e))},reports:{getSummary:async e=>{try{let t=await r.get("/reports/summary",{params:e});if(!t||"object"!=typeof t)return console.warn("Invalid summary response, using default values"),{totalIncome:0,totalExpense:0,netBalance:0,topExpenseCategories:[],topIncomeCategories:[]};return t}catch(e){return console.error("Error fetching summary:",e),{totalIncome:0,totalExpense:0,netBalance:0,topExpenseCategories:[],topIncomeCategories:[]}}},getCategoryBreakdown:async e=>{try{let t=await r.get("/reports/category-breakdown",{params:e});if(!t||"object"!=typeof t)return{expenseCategories:[],incomeCategories:[]};return t}catch(e){return console.error("Error fetching category breakdown:",e),{expenseCategories:[],incomeCategories:[]}}},getMonthlyReport:async e=>{try{let t=await r.get("/reports/monthly",{params:e});if(!t||"object"!=typeof t)return{months:[]};return t}catch(e){return console.error("Error fetching monthly report:",e),{months:[]}}},getLocationSummary:async e=>{try{let t=await r.get("/reports/location-summary",{params:e});if(!t||"object"!=typeof t)return{locations:[]};return t}catch(e){return console.error("Error fetching location summary:",e),{locations:[]}}}},bankStatements:{upload:e=>r.post("/bank-statements/upload",e,{headers:{"Content-Type":"multipart/form-data"}}),import:e=>r.post("/bank-statements/import",e)}}},30285:(e,t,a)=>{"use strict";a.d(t,{$:()=>i});var r=a(95155);a(12115);var n=a(99708),s=a(74466),o=a(59434);let l=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:t,variant:a,size:s,asChild:i=!1,...c}=e,d=i?n.DX:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,o.cn)(l({variant:a,size:s,className:t})),...c})}},59434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>s});var r=a(52596),n=a(39688);function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,n.QP)((0,r.$)(t))}},62523:(e,t,a)=>{"use strict";a.d(t,{p:()=>s});var r=a(95155);a(12115);var n=a(59434);function s(e){let{className:t,type:a,...s}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...s})}},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>i,Wu:()=>c,ZB:()=>l,Zp:()=>s,aR:()=>o,wL:()=>d});var r=a(95155);a(12115);var n=a(59434);function s(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...a})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},75937:(e,t,a)=>{"use strict";a.d(t,{lV:()=>d,MJ:()=>f,zB:()=>x,eI:()=>p,lR:()=>g,C5:()=>j});var r=a(95155),n=a(12115),s=a(99708),o=a(62177),l=a(59434),i=a(40968);function c(e){let{className:t,...a}=e;return(0,r.jsx)(i.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}let d=o.Op,u=n.createContext({}),x=e=>{let{...t}=e;return(0,r.jsx)(u.Provider,{value:{name:t.name},children:(0,r.jsx)(o.xI,{...t})})},m=()=>{let e=n.useContext(u),t=n.useContext(h),{getFieldState:a}=(0,o.xW)(),r=(0,o.lN)({name:e.name}),s=a(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:"".concat(l,"-form-item"),formDescriptionId:"".concat(l,"-form-item-description"),formMessageId:"".concat(l,"-form-item-message"),...s}},h=n.createContext({});function p(e){let{className:t,...a}=e,s=n.useId();return(0,r.jsx)(h.Provider,{value:{id:s},children:(0,r.jsx)("div",{"data-slot":"form-item",className:(0,l.cn)("grid gap-2",t),...a})})}function g(e){let{className:t,...a}=e,{error:n,formItemId:s}=m();return(0,r.jsx)(c,{"data-slot":"form-label","data-error":!!n,className:(0,l.cn)("data-[error=true]:text-destructive",t),htmlFor:s,...a})}function f(e){let{...t}=e,{error:a,formItemId:n,formDescriptionId:o,formMessageId:l}=m();return(0,r.jsx)(s.DX,{"data-slot":"form-control",id:n,"aria-describedby":a?"".concat(o," ").concat(l):"".concat(o),"aria-invalid":!!a,...t})}function j(e){var t;let{className:a,...n}=e,{error:s,formMessageId:o}=m(),i=s?String(null!=(t=null==s?void 0:s.message)?t:""):n.children;return i?(0,r.jsx)("p",{"data-slot":"form-message",id:o,className:(0,l.cn)("text-destructive text-sm",a),...n,children:i}):null}},89390:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>ee});var r=a(95155),n=a(12115),s=a(35695),o=a(13319),l=a(59434);function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,l.cn)("w-full caption-bottom text-sm",t),...a})})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,l.cn)("[&_tr]:border-b",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,l.cn)("[&_tr:last-child]:border-0",t),...a})}function u(e){let{className:t,...a}=e;return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,l.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...a})}function x(e){let{className:t,...a}=e;return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,l.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}function m(e){let{className:t,...a}=e;return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,l.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}var h=a(30285),p=a(62523),g=a(66695),f=a(15452),j=a(54416);function v(e){let{...t}=e;return(0,r.jsx)(f.bL,{"data-slot":"dialog",...t})}function y(e){let{...t}=e;return(0,r.jsx)(f.l9,{"data-slot":"dialog-trigger",...t})}function b(e){let{...t}=e;return(0,r.jsx)(f.ZL,{"data-slot":"dialog-portal",...t})}function w(e){let{className:t,...a}=e;return(0,r.jsx)(f.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function N(e){let{className:t,children:a,...n}=e;return(0,r.jsxs)(b,{"data-slot":"dialog-portal",children:[(0,r.jsx)(w,{}),(0,r.jsxs)(f.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...n,children:[a,(0,r.jsxs)(f.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(j.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function C(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function z(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function _(e){let{className:t,...a}=e;return(0,r.jsx)(f.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",t),...a})}function k(e){let{className:t,...a}=e;return(0,r.jsx)(f.VY,{"data-slot":"dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",t),...a})}var A=a(78893),I=a(66474),R=a(5196),S=a(47863);function E(e){let{...t}=e;return(0,r.jsx)(A.bL,{"data-slot":"select",...t})}function B(e){let{...t}=e;return(0,r.jsx)(A.WT,{"data-slot":"select-value",...t})}function M(e){let{className:t,size:a="default",children:n,...s}=e;return(0,r.jsxs)(A.l9,{"data-slot":"select-trigger","data-size":a,className:(0,l.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...s,children:[n,(0,r.jsx)(A.In,{asChild:!0,children:(0,r.jsx)(I.A,{className:"size-4 opacity-50"})})]})}function T(e){let{className:t,children:a,position:n="popper",...s}=e;return(0,r.jsx)(A.ZL,{children:(0,r.jsxs)(A.UC,{"data-slot":"select-content",className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...s,children:[(0,r.jsx)(V,{}),(0,r.jsx)(A.LM,{className:(0,l.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,r.jsx)(L,{})]})})}function P(e){let{className:t,children:a,...n}=e;return(0,r.jsxs)(A.q7,{"data-slot":"select-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...n,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(A.VF,{children:(0,r.jsx)(R.A,{className:"size-4"})})}),(0,r.jsx)(A.p4,{children:a})]})}function V(e){let{className:t,...a}=e;return(0,r.jsx)(A.PP,{"data-slot":"select-scroll-up-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(S.A,{className:"size-4"})})}function L(e){let{className:t,...a}=e;return(0,r.jsx)(A.wn,{"data-slot":"select-scroll-down-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(I.A,{className:"size-4"})})}var F=a(75937);function D(e){let{className:t,...a}=e;return(0,r.jsx)("textarea",{"data-slot":"textarea",className:(0,l.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...a})}var J=a(90221),U=a(62177),q=a(71153),O=a(84616),Z=a(51154),$=a(47924),G=a(39881),W=a(58832),Y=a(13717),X=a(62525),Q=a(56671),H=a(25731);let K=q.z.object({title:q.z.string().min(1,"Title is required"),type:q.z.enum(["income","expense"]),amount:q.z.coerce.number().positive("Amount must be positive"),currency:q.z.string().min(1,"Currency is required"),category_id:q.z.string().min(1,"Category is required"),payment_method:q.z.string().min(1,"Payment method is required"),account_id:q.z.string().min(1,"Account is required"),note:q.z.string().optional(),transaction_date:q.z.string().min(1,"Date is required"),location:q.z.string().optional()});function ee(){(0,s.useRouter)();let[e,t]=(0,n.useState)([]),[a,l]=(0,n.useState)([]),[f,j]=(0,n.useState)([]),[b,w]=(0,n.useState)(!0),[A,I]=(0,n.useState)(!1),[R,S]=(0,n.useState)(!1),[V,L]=(0,n.useState)(null),[q,ee]=(0,n.useState)(""),[et,ea]=(0,n.useState)("all"),er=(0,U.mN)({resolver:(0,J.u)(K),defaultValues:{title:"",type:"expense",amount:0,currency:"USD",category_id:"",payment_method:"cash",account_id:"",note:"",transaction_date:(0,o.GP)(new Date,"yyyy-MM-dd"),location:""}});(0,n.useEffect)(()=>{(async()=>{w(!0);try{let e=await H.A.transactions.getAll();t(e);let a=await H.A.categories.getAll();l(a);let r=await H.A.accounts.getAll();j(r)}catch(e){console.error("Error fetching data:",e),Q.oR.error("Failed to load data")}finally{w(!1)}})()},[]);let en=async e=>{I(!0);try{V?(await H.A.transactions.update(V.id,e),Q.oR.success("Transaction updated successfully")):(await H.A.transactions.create(e),Q.oR.success("Transaction created successfully"));let a=await H.A.transactions.getAll();t(a),S(!1),er.reset(),L(null)}catch(e){console.error("Error saving transaction:",e),Q.oR.error("string"==typeof e?e:"Failed to save transaction")}finally{I(!1)}},es=e=>{L(e),er.reset({title:e.title,type:e.type,amount:e.amount,currency:e.currency,category_id:e.category_id,payment_method:e.payment_method,account_id:e.account_id,note:e.note||"",transaction_date:(0,o.GP)(new Date(e.transaction_date),"yyyy-MM-dd"),location:e.location||""}),S(!0)},eo=async e=>{if(confirm("Are you sure you want to delete this transaction?"))try{await H.A.transactions.delete(e),Q.oR.success("Transaction deleted successfully");let a=await H.A.transactions.getAll();t(a)}catch(e){console.error("Error deleting transaction:",e),Q.oR.error("string"==typeof e?e:"Failed to delete transaction")}},el=(e,t)=>new Intl.NumberFormat("en-US",{style:"currency",currency:t}).format(e),ei=e.filter(e=>{if("all"!==et&&e.type!==et)return!1;if(q){var t;let a=q.toLowerCase();return e.title.toLowerCase().includes(a)||(null==(t=e.note)?void 0:t.toLowerCase().includes(a))||e.category.name.toLowerCase().includes(a)||e.account.name.toLowerCase().includes(a)||e.payment_method.toLowerCase().includes(a)}return!0});return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-3xl font-bold tracking-tight",children:"Transactions"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Manage your income and expenses"})]}),(0,r.jsxs)(v,{open:R,onOpenChange:S,children:[(0,r.jsx)(y,{asChild:!0,children:(0,r.jsxs)(h.$,{children:[(0,r.jsx)(O.A,{className:"mr-2 h-4 w-4"}),"Add Transaction"]})}),(0,r.jsxs)(N,{className:"sm:max-w-[600px]",children:[(0,r.jsxs)(C,{children:[(0,r.jsx)(_,{children:V?"Edit Transaction":"Add New Transaction"}),(0,r.jsx)(k,{children:V?"Update the transaction details below":"Fill in the details to add a new transaction"})]}),(0,r.jsx)(F.lV,{...er,children:(0,r.jsxs)("form",{onSubmit:er.handleSubmit(en),className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(F.zB,{control:er.control,name:"title",render:e=>{let{field:t}=e;return(0,r.jsxs)(F.eI,{children:[(0,r.jsx)(F.lR,{children:"Title"}),(0,r.jsx)(F.MJ,{children:(0,r.jsx)(p.p,{placeholder:"Transaction title",...t})}),(0,r.jsx)(F.C5,{})]})}}),(0,r.jsx)(F.zB,{control:er.control,name:"type",render:e=>{let{field:t}=e;return(0,r.jsxs)(F.eI,{children:[(0,r.jsx)(F.lR,{children:"Type"}),(0,r.jsxs)(E,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,r.jsx)(F.MJ,{children:(0,r.jsx)(M,{children:(0,r.jsx)(B,{placeholder:"Select type"})})}),(0,r.jsxs)(T,{children:[(0,r.jsx)(P,{value:"expense",children:"Expense"}),(0,r.jsx)(P,{value:"income",children:"Income"})]})]}),(0,r.jsx)(F.C5,{})]})}}),(0,r.jsx)(F.zB,{control:er.control,name:"amount",render:e=>{let{field:t}=e;return(0,r.jsxs)(F.eI,{children:[(0,r.jsx)(F.lR,{children:"Amount"}),(0,r.jsx)(F.MJ,{children:(0,r.jsx)(p.p,{type:"number",placeholder:"0.00",...t,onChange:e=>t.onChange(parseFloat(e.target.value))})}),(0,r.jsx)(F.C5,{})]})}}),(0,r.jsx)(F.zB,{control:er.control,name:"currency",render:e=>{let{field:t}=e;return(0,r.jsxs)(F.eI,{children:[(0,r.jsx)(F.lR,{children:"Currency"}),(0,r.jsxs)(E,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,r.jsx)(F.MJ,{children:(0,r.jsx)(M,{children:(0,r.jsx)(B,{placeholder:"Select currency"})})}),(0,r.jsxs)(T,{children:[(0,r.jsx)(P,{value:"USD",children:"USD"}),(0,r.jsx)(P,{value:"EUR",children:"EUR"}),(0,r.jsx)(P,{value:"GBP",children:"GBP"}),(0,r.jsx)(P,{value:"TRY",children:"TRY"})]})]}),(0,r.jsx)(F.C5,{})]})}}),(0,r.jsx)(F.zB,{control:er.control,name:"category_id",render:e=>{let{field:t}=e;return(0,r.jsxs)(F.eI,{children:[(0,r.jsx)(F.lR,{children:"Category"}),(0,r.jsxs)(E,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,r.jsx)(F.MJ,{children:(0,r.jsx)(M,{children:(0,r.jsx)(B,{placeholder:"Select category"})})}),(0,r.jsx)(T,{children:a.filter(e=>e.type===er.getValues("type")).map(e=>(0,r.jsxs)(P,{value:e.id,children:[e.icon," ",e.name]},e.id))})]}),(0,r.jsx)(F.C5,{})]})}}),(0,r.jsx)(F.zB,{control:er.control,name:"account_id",render:e=>{let{field:t}=e;return(0,r.jsxs)(F.eI,{children:[(0,r.jsx)(F.lR,{children:"Account"}),(0,r.jsxs)(E,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,r.jsx)(F.MJ,{children:(0,r.jsx)(M,{children:(0,r.jsx)(B,{placeholder:"Select account"})})}),(0,r.jsx)(T,{children:f.map(e=>(0,r.jsx)(P,{value:e.id,children:e.name},e.id))})]}),(0,r.jsx)(F.C5,{})]})}}),(0,r.jsx)(F.zB,{control:er.control,name:"payment_method",render:e=>{let{field:t}=e;return(0,r.jsxs)(F.eI,{children:[(0,r.jsx)(F.lR,{children:"Payment Method"}),(0,r.jsxs)(E,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,r.jsx)(F.MJ,{children:(0,r.jsx)(M,{children:(0,r.jsx)(B,{placeholder:"Select payment method"})})}),(0,r.jsxs)(T,{children:[(0,r.jsx)(P,{value:"cash",children:"Cash"}),(0,r.jsx)(P,{value:"credit_card",children:"Credit Card"}),(0,r.jsx)(P,{value:"debit_card",children:"Debit Card"}),(0,r.jsx)(P,{value:"bank_transfer",children:"Bank Transfer"}),(0,r.jsx)(P,{value:"mobile_payment",children:"Mobile Payment"})]})]}),(0,r.jsx)(F.C5,{})]})}}),(0,r.jsx)(F.zB,{control:er.control,name:"transaction_date",render:e=>{let{field:t}=e;return(0,r.jsxs)(F.eI,{children:[(0,r.jsx)(F.lR,{children:"Date"}),(0,r.jsx)(F.MJ,{children:(0,r.jsx)(p.p,{type:"date",...t})}),(0,r.jsx)(F.C5,{})]})}}),(0,r.jsx)(F.zB,{control:er.control,name:"location",render:e=>{let{field:t}=e;return(0,r.jsxs)(F.eI,{children:[(0,r.jsx)(F.lR,{children:"Location (Optional)"}),(0,r.jsx)(F.MJ,{children:(0,r.jsx)(p.p,{placeholder:"Location",...t})}),(0,r.jsx)(F.C5,{})]})}})]}),(0,r.jsx)(F.zB,{control:er.control,name:"note",render:e=>{let{field:t}=e;return(0,r.jsxs)(F.eI,{children:[(0,r.jsx)(F.lR,{children:"Note (Optional)"}),(0,r.jsx)(F.MJ,{children:(0,r.jsx)(D,{placeholder:"Add any additional notes",className:"resize-none",...t})}),(0,r.jsx)(F.C5,{})]})}}),(0,r.jsx)(z,{children:(0,r.jsx)(h.$,{type:"submit",disabled:A,children:A?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(Z.A,{className:"mr-2 h-4 w-4 animate-spin"}),V?"Updating...":"Creating..."]}):V?"Update Transaction":"Create Transaction"})})]})})]})]})]}),(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)($.A,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,r.jsx)(p.p,{placeholder:"Search transactions...",className:"pl-9",value:q,onChange:e=>ee(e.target.value)})]}),(0,r.jsxs)(E,{value:et,onValueChange:ea,children:[(0,r.jsx)(M,{className:"w-full md:w-[180px]",children:(0,r.jsx)(B,{placeholder:"Filter by type"})}),(0,r.jsxs)(T,{children:[(0,r.jsx)(P,{value:"all",children:"All Types"}),(0,r.jsx)(P,{value:"income",children:"Income"}),(0,r.jsx)(P,{value:"expense",children:"Expense"})]})]})]}),(0,r.jsxs)(g.Zp,{children:[(0,r.jsx)(g.aR,{children:(0,r.jsx)(g.ZB,{children:"Recent Transactions"})}),(0,r.jsx)(g.Wu,{children:b?(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary"})}):0===ei.length?(0,r.jsx)("div",{className:"text-center py-10",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"No transactions found"})}):(0,r.jsxs)(i,{children:[(0,r.jsx)(c,{children:(0,r.jsxs)(u,{children:[(0,r.jsx)(x,{children:"Date"}),(0,r.jsx)(x,{children:"Title"}),(0,r.jsx)(x,{children:"Category"}),(0,r.jsx)(x,{children:"Account"}),(0,r.jsx)(x,{className:"text-right",children:"Amount"}),(0,r.jsx)(x,{className:"text-right",children:"Actions"})]})}),(0,r.jsx)(d,{children:ei.map(e=>(0,r.jsxs)(u,{children:[(0,r.jsx)(m,{children:(0,o.GP)(new Date(e.transaction_date),"MMM dd, yyyy")}),(0,r.jsx)(m,{children:e.title}),(0,r.jsx)(m,{children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{children:e.category.icon}),(0,r.jsx)("span",{children:e.category.name})]})}),(0,r.jsx)(m,{children:e.account.name}),(0,r.jsx)(m,{className:"text-right",children:(0,r.jsxs)("div",{className:"flex items-center justify-end gap-1 font-medium ".concat("income"===e.type?"text-green-600":"text-red-600"),children:["income"===e.type?(0,r.jsx)(G.A,{className:"h-4 w-4"}):(0,r.jsx)(W.A,{className:"h-4 w-4"}),el(e.amount,e.currency)]})}),(0,r.jsx)(m,{className:"text-right",children:(0,r.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,r.jsxs)(h.$,{variant:"ghost",size:"icon",onClick:()=>es(e),children:[(0,r.jsx)(Y.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Edit"})]}),(0,r.jsxs)(h.$,{variant:"ghost",size:"icon",className:"text-red-600",onClick:()=>eo(e.id),children:[(0,r.jsx)(X.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Delete"})]})]})})]},e.id))})]})})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[464,204,753,146,550,641,319,444,441,684,358],()=>t(10814)),_N_E=e.O()}]);