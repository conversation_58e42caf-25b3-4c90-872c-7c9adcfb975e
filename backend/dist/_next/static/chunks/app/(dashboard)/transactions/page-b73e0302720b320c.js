(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[486],{10814:(e,t,n)=>{Promise.resolve().then(n.bind(n,85590))},15452:(e,t,n)=>{"use strict";n.d(t,{UC:()=>en,VY:()=>er,ZL:()=>ee,bL:()=>X,bm:()=>es,hE:()=>ea,hJ:()=>et,l9:()=>Q});var a=n(12115),r=n(85185),s=n(6101),l=n(46081),o=n(61285),i=n(5845),c=n(19178),d=n(25519),u=n(34378),x=n(28905),h=n(63655),m=n(92293),p=n(93795),j=n(38168),f=n(99708),g=n(95155),y="Dialog",[v,b]=(0,l.A)(y),[C,w]=v(y),N=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:s,onOpenChange:l,modal:c=!0}=e,d=a.useRef(null),u=a.useRef(null),[x,h]=(0,i.i)({prop:r,defaultProp:null!=s&&s,onChange:l,caller:y});return(0,g.jsx)(C,{scope:t,triggerRef:d,contentRef:u,contentId:(0,o.B)(),titleId:(0,o.B)(),descriptionId:(0,o.B)(),open:x,onOpenChange:h,onOpenToggle:a.useCallback(()=>h(e=>!e),[h]),modal:c,children:n})};N.displayName=y;var _="DialogTrigger",R=a.forwardRef((e,t)=>{let{__scopeDialog:n,...a}=e,l=w(_,n),o=(0,s.s)(t,l.triggerRef);return(0,g.jsx)(h.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":W(l.open),...a,ref:o,onClick:(0,r.m)(e.onClick,l.onOpenToggle)})});R.displayName=_;var A="DialogPortal",[k,D]=v(A,{forceMount:void 0}),I=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:s}=e,l=w(A,t);return(0,g.jsx)(k,{scope:t,forceMount:n,children:a.Children.map(r,e=>(0,g.jsx)(x.C,{present:n||l.open,children:(0,g.jsx)(u.Z,{asChild:!0,container:s,children:e})}))})};I.displayName=A;var M="DialogOverlay",z=a.forwardRef((e,t)=>{let n=D(M,e.__scopeDialog),{forceMount:a=n.forceMount,...r}=e,s=w(M,e.__scopeDialog);return s.modal?(0,g.jsx)(x.C,{present:a||s.open,children:(0,g.jsx)(F,{...r,ref:t})}):null});z.displayName=M;var E=(0,f.TL)("DialogOverlay.RemoveScroll"),F=a.forwardRef((e,t)=>{let{__scopeDialog:n,...a}=e,r=w(M,n);return(0,g.jsx)(p.A,{as:E,allowPinchZoom:!0,shards:[r.contentRef],children:(0,g.jsx)(h.sG.div,{"data-state":W(r.open),...a,ref:t,style:{pointerEvents:"auto",...a.style}})})}),S="DialogContent",T=a.forwardRef((e,t)=>{let n=D(S,e.__scopeDialog),{forceMount:a=n.forceMount,...r}=e,s=w(S,e.__scopeDialog);return(0,g.jsx)(x.C,{present:a||s.open,children:s.modal?(0,g.jsx)(O,{...r,ref:t}):(0,g.jsx)(V,{...r,ref:t})})});T.displayName=S;var O=a.forwardRef((e,t)=>{let n=w(S,e.__scopeDialog),l=a.useRef(null),o=(0,s.s)(t,n.contentRef,l);return a.useEffect(()=>{let e=l.current;if(e)return(0,j.Eq)(e)},[]),(0,g.jsx)(B,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,r.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,r.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,r.m)(e.onFocusOutside,e=>e.preventDefault())})}),V=a.forwardRef((e,t)=>{let n=w(S,e.__scopeDialog),r=a.useRef(!1),s=a.useRef(!1);return(0,g.jsx)(B,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var a,l;null==(a=e.onCloseAutoFocus)||a.call(e,t),t.defaultPrevented||(r.current||null==(l=n.triggerRef.current)||l.focus(),t.preventDefault()),r.current=!1,s.current=!1},onInteractOutside:t=>{var a,l;null==(a=e.onInteractOutside)||a.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"===t.detail.originalEvent.type&&(s.current=!0));let o=t.target;(null==(l=n.triggerRef.current)?void 0:l.contains(o))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&s.current&&t.preventDefault()}})}),B=a.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:l,onCloseAutoFocus:o,...i}=e,u=w(S,n),x=a.useRef(null),h=(0,s.s)(t,x);return(0,m.Oh)(),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(d.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:l,onUnmountAutoFocus:o,children:(0,g.jsx)(c.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":W(u.open),...i,ref:h,onDismiss:()=>u.onOpenChange(!1)})}),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(H,{titleId:u.titleId}),(0,g.jsx)(K,{contentRef:x,descriptionId:u.descriptionId})]})]})}),P="DialogTitle",q=a.forwardRef((e,t)=>{let{__scopeDialog:n,...a}=e,r=w(P,n);return(0,g.jsx)(h.sG.h2,{id:r.titleId,...a,ref:t})});q.displayName=P;var J="DialogDescription",L=a.forwardRef((e,t)=>{let{__scopeDialog:n,...a}=e,r=w(J,n);return(0,g.jsx)(h.sG.p,{id:r.descriptionId,...a,ref:t})});L.displayName=J;var U="DialogClose",G=a.forwardRef((e,t)=>{let{__scopeDialog:n,...a}=e,s=w(U,n);return(0,g.jsx)(h.sG.button,{type:"button",...a,ref:t,onClick:(0,r.m)(e.onClick,()=>s.onOpenChange(!1))})});function W(e){return e?"open":"closed"}G.displayName=U;var Z="DialogTitleWarning",[Y,$]=(0,l.q)(Z,{contentName:S,titleName:P,docsSlug:"dialog"}),H=e=>{let{titleId:t}=e,n=$(Z),r="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return a.useEffect(()=>{t&&(document.getElementById(t)||console.error(r))},[r,t]),null},K=e=>{let{contentRef:t,descriptionId:n}=e,r=$("DialogDescriptionWarning"),s="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return a.useEffect(()=>{var e;let a=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&a&&(document.getElementById(n)||console.warn(s))},[s,t,n]),null},X=N,Q=R,ee=I,et=z,en=T,ea=q,er=L,es=G},17759:(e,t,n)=>{"use strict";n.d(t,{C5:()=>f,MJ:()=>j,eI:()=>m,lR:()=>p,lV:()=>c,zB:()=>u});var a=n(95155),r=n(12115),s=n(99708),l=n(62177),o=n(59434),i=n(85057);let c=l.Op,d=r.createContext({}),u=e=>{let{...t}=e;return(0,a.jsx)(d.Provider,{value:{name:t.name},children:(0,a.jsx)(l.xI,{...t})})},x=()=>{let e=r.useContext(d),t=r.useContext(h),{getFieldState:n}=(0,l.xW)(),a=(0,l.lN)({name:e.name}),s=n(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:"".concat(o,"-form-item"),formDescriptionId:"".concat(o,"-form-item-description"),formMessageId:"".concat(o,"-form-item-message"),...s}},h=r.createContext({});function m(e){let{className:t,...n}=e,s=r.useId();return(0,a.jsx)(h.Provider,{value:{id:s},children:(0,a.jsx)("div",{"data-slot":"form-item",className:(0,o.cn)("grid gap-2",t),...n})})}function p(e){let{className:t,...n}=e,{error:r,formItemId:s}=x();return(0,a.jsx)(i.J,{"data-slot":"form-label","data-error":!!r,className:(0,o.cn)("data-[error=true]:text-destructive",t),htmlFor:s,...n})}function j(e){let{...t}=e,{error:n,formItemId:r,formDescriptionId:l,formMessageId:o}=x();return(0,a.jsx)(s.DX,{"data-slot":"form-control",id:r,"aria-describedby":n?"".concat(l," ").concat(o):"".concat(l),"aria-invalid":!!n,...t})}function f(e){var t;let{className:n,...r}=e,{error:s,formMessageId:l}=x(),i=s?String(null!=(t=null==s?void 0:s.message)?t:""):r.children;return i?(0,a.jsx)("p",{"data-slot":"form-message",id:l,className:(0,o.cn)("text-destructive text-sm",n),...r,children:i}):null}},54416:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});let a=(0,n(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},85590:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>G});var a=n(95155),r=n(12115),s=n(35695),l=n(13319),o=n(59434);function i(e){let{className:t,...n}=e;return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,o.cn)("w-full caption-bottom text-sm",t),...n})})}function c(e){let{className:t,...n}=e;return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,o.cn)("[&_tr]:border-b",t),...n})}function d(e){let{className:t,...n}=e;return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,o.cn)("[&_tr:last-child]:border-0",t),...n})}function u(e){let{className:t,...n}=e;return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,o.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...n})}function x(e){let{className:t,...n}=e;return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,o.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...n})}function h(e){let{className:t,...n}=e;return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,o.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...n})}var m=n(30285),p=n(62523),j=n(66695),f=n(15452),g=n(54416);function y(e){let{...t}=e;return(0,a.jsx)(f.bL,{"data-slot":"dialog",...t})}function v(e){let{...t}=e;return(0,a.jsx)(f.l9,{"data-slot":"dialog-trigger",...t})}function b(e){let{...t}=e;return(0,a.jsx)(f.ZL,{"data-slot":"dialog-portal",...t})}function C(e){let{className:t,...n}=e;return(0,a.jsx)(f.hJ,{"data-slot":"dialog-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...n})}function w(e){let{className:t,children:n,...r}=e;return(0,a.jsxs)(b,{"data-slot":"dialog-portal",children:[(0,a.jsx)(C,{}),(0,a.jsxs)(f.UC,{"data-slot":"dialog-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...r,children:[n,(0,a.jsxs)(f.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,a.jsx)(g.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function N(e){let{className:t,...n}=e;return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,o.cn)("flex flex-col gap-2 text-center sm:text-left",t),...n})}function _(e){let{className:t,...n}=e;return(0,a.jsx)("div",{"data-slot":"dialog-footer",className:(0,o.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...n})}function R(e){let{className:t,...n}=e;return(0,a.jsx)(f.hE,{"data-slot":"dialog-title",className:(0,o.cn)("text-lg leading-none font-semibold",t),...n})}function A(e){let{className:t,...n}=e;return(0,a.jsx)(f.VY,{"data-slot":"dialog-description",className:(0,o.cn)("text-muted-foreground text-sm",t),...n})}var k=n(59409),D=n(17759);function I(e){let{className:t,...n}=e;return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,o.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...n})}var M=n(90221),z=n(62177),E=n(71153),F=n(19946);let S=(0,F.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var T=n(51154);let O=(0,F.A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]),V=(0,F.A)("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]),B=(0,F.A)("arrow-down",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]),P=(0,F.A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),q=(0,F.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);var J=n(56671),L=n(25731);let U=E.z.object({title:E.z.string().min(1,"Title is required"),type:E.z.enum(["income","expense"]),amount:E.z.coerce.number().positive("Amount must be positive"),currency:E.z.string().min(1,"Currency is required"),category_id:E.z.string().min(1,"Category is required"),payment_method:E.z.string().min(1,"Payment method is required"),account_id:E.z.string().min(1,"Account is required"),note:E.z.string().optional(),transaction_date:E.z.string().min(1,"Date is required"),location:E.z.string().optional()});function G(){(0,s.useRouter)();let[e,t]=(0,r.useState)([]),[n,o]=(0,r.useState)([]),[f,g]=(0,r.useState)([]),[b,C]=(0,r.useState)(!0),[E,F]=(0,r.useState)(!1),[G,W]=(0,r.useState)(!1),[Z,Y]=(0,r.useState)(null),[$,H]=(0,r.useState)(""),[K,X]=(0,r.useState)("all"),Q=(0,z.mN)({resolver:(0,M.u)(U),defaultValues:{title:"",type:"expense",amount:0,currency:"USD",category_id:"",payment_method:"cash",account_id:"",note:"",transaction_date:(0,l.GP)(new Date,"yyyy-MM-dd"),location:""}});(0,r.useEffect)(()=>{(async()=>{C(!0);try{let e=await L.A.transactions.getAll();t(e);let n=await L.A.categories.getAll();o(n);let a=await L.A.accounts.getAll();g(a)}catch(e){console.error("Error fetching data:",e),J.oR.error("Failed to load data")}finally{C(!1)}})()},[]);let ee=async e=>{F(!0);try{Z?(await L.A.transactions.update(Z.id,e),J.oR.success("Transaction updated successfully")):(await L.A.transactions.create(e),J.oR.success("Transaction created successfully"));let n=await L.A.transactions.getAll();t(n),W(!1),Q.reset(),Y(null)}catch(e){console.error("Error saving transaction:",e),J.oR.error("string"==typeof e?e:"Failed to save transaction")}finally{F(!1)}},et=e=>{Y(e),Q.reset({title:e.title,type:e.type,amount:e.amount,currency:e.currency,category_id:e.category_id,payment_method:e.payment_method,account_id:e.account_id,note:e.note||"",transaction_date:(0,l.GP)(new Date(e.transaction_date),"yyyy-MM-dd"),location:e.location||""}),W(!0)},en=async e=>{if(confirm("Are you sure you want to delete this transaction?"))try{await L.A.transactions.delete(e),J.oR.success("Transaction deleted successfully");let n=await L.A.transactions.getAll();t(n)}catch(e){console.error("Error deleting transaction:",e),J.oR.error("string"==typeof e?e:"Failed to delete transaction")}},ea=(e,t)=>new Intl.NumberFormat("en-US",{style:"currency",currency:t}).format(e),er=e.filter(e=>{if("all"!==K&&e.type!==K)return!1;if($){var t;let n=$.toLowerCase();return e.title.toLowerCase().includes(n)||(null==(t=e.note)?void 0:t.toLowerCase().includes(n))||e.category.name.toLowerCase().includes(n)||e.account.name.toLowerCase().includes(n)||e.payment_method.toLowerCase().includes(n)}return!0});return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-3xl font-bold tracking-tight",children:"Transactions"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage your income and expenses"})]}),(0,a.jsxs)(y,{open:G,onOpenChange:W,children:[(0,a.jsx)(v,{asChild:!0,children:(0,a.jsxs)(m.$,{children:[(0,a.jsx)(S,{className:"mr-2 h-4 w-4"}),"Add Transaction"]})}),(0,a.jsxs)(w,{className:"sm:max-w-[600px]",children:[(0,a.jsxs)(N,{children:[(0,a.jsx)(R,{children:Z?"Edit Transaction":"Add New Transaction"}),(0,a.jsx)(A,{children:Z?"Update the transaction details below":"Fill in the details to add a new transaction"})]}),(0,a.jsx)(D.lV,{...Q,children:(0,a.jsxs)("form",{onSubmit:Q.handleSubmit(ee),className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsx)(D.zB,{control:Q.control,name:"title",render:e=>{let{field:t}=e;return(0,a.jsxs)(D.eI,{children:[(0,a.jsx)(D.lR,{children:"Title"}),(0,a.jsx)(D.MJ,{children:(0,a.jsx)(p.p,{placeholder:"Transaction title",...t})}),(0,a.jsx)(D.C5,{})]})}}),(0,a.jsx)(D.zB,{control:Q.control,name:"type",render:e=>{let{field:t}=e;return(0,a.jsxs)(D.eI,{children:[(0,a.jsx)(D.lR,{children:"Type"}),(0,a.jsxs)(k.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,a.jsx)(D.MJ,{children:(0,a.jsx)(k.bq,{children:(0,a.jsx)(k.yv,{placeholder:"Select type"})})}),(0,a.jsxs)(k.gC,{children:[(0,a.jsx)(k.eb,{value:"expense",children:"Expense"}),(0,a.jsx)(k.eb,{value:"income",children:"Income"})]})]}),(0,a.jsx)(D.C5,{})]})}}),(0,a.jsx)(D.zB,{control:Q.control,name:"amount",render:e=>{let{field:t}=e;return(0,a.jsxs)(D.eI,{children:[(0,a.jsx)(D.lR,{children:"Amount"}),(0,a.jsx)(D.MJ,{children:(0,a.jsx)(p.p,{type:"number",placeholder:"0.00",...t,onChange:e=>t.onChange(parseFloat(e.target.value))})}),(0,a.jsx)(D.C5,{})]})}}),(0,a.jsx)(D.zB,{control:Q.control,name:"currency",render:e=>{let{field:t}=e;return(0,a.jsxs)(D.eI,{children:[(0,a.jsx)(D.lR,{children:"Currency"}),(0,a.jsxs)(k.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,a.jsx)(D.MJ,{children:(0,a.jsx)(k.bq,{children:(0,a.jsx)(k.yv,{placeholder:"Select currency"})})}),(0,a.jsxs)(k.gC,{children:[(0,a.jsx)(k.eb,{value:"USD",children:"USD"}),(0,a.jsx)(k.eb,{value:"EUR",children:"EUR"}),(0,a.jsx)(k.eb,{value:"GBP",children:"GBP"}),(0,a.jsx)(k.eb,{value:"TRY",children:"TRY"})]})]}),(0,a.jsx)(D.C5,{})]})}}),(0,a.jsx)(D.zB,{control:Q.control,name:"category_id",render:e=>{let{field:t}=e;return(0,a.jsxs)(D.eI,{children:[(0,a.jsx)(D.lR,{children:"Category"}),(0,a.jsxs)(k.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,a.jsx)(D.MJ,{children:(0,a.jsx)(k.bq,{children:(0,a.jsx)(k.yv,{placeholder:"Select category"})})}),(0,a.jsx)(k.gC,{children:n.filter(e=>e.type===Q.getValues("type")).map(e=>(0,a.jsxs)(k.eb,{value:e.id,children:[e.icon," ",e.name]},e.id))})]}),(0,a.jsx)(D.C5,{})]})}}),(0,a.jsx)(D.zB,{control:Q.control,name:"account_id",render:e=>{let{field:t}=e;return(0,a.jsxs)(D.eI,{children:[(0,a.jsx)(D.lR,{children:"Account"}),(0,a.jsxs)(k.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,a.jsx)(D.MJ,{children:(0,a.jsx)(k.bq,{children:(0,a.jsx)(k.yv,{placeholder:"Select account"})})}),(0,a.jsx)(k.gC,{children:f.map(e=>(0,a.jsx)(k.eb,{value:e.id,children:e.name},e.id))})]}),(0,a.jsx)(D.C5,{})]})}}),(0,a.jsx)(D.zB,{control:Q.control,name:"payment_method",render:e=>{let{field:t}=e;return(0,a.jsxs)(D.eI,{children:[(0,a.jsx)(D.lR,{children:"Payment Method"}),(0,a.jsxs)(k.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,a.jsx)(D.MJ,{children:(0,a.jsx)(k.bq,{children:(0,a.jsx)(k.yv,{placeholder:"Select payment method"})})}),(0,a.jsxs)(k.gC,{children:[(0,a.jsx)(k.eb,{value:"cash",children:"Cash"}),(0,a.jsx)(k.eb,{value:"credit_card",children:"Credit Card"}),(0,a.jsx)(k.eb,{value:"debit_card",children:"Debit Card"}),(0,a.jsx)(k.eb,{value:"bank_transfer",children:"Bank Transfer"}),(0,a.jsx)(k.eb,{value:"mobile_payment",children:"Mobile Payment"})]})]}),(0,a.jsx)(D.C5,{})]})}}),(0,a.jsx)(D.zB,{control:Q.control,name:"transaction_date",render:e=>{let{field:t}=e;return(0,a.jsxs)(D.eI,{children:[(0,a.jsx)(D.lR,{children:"Date"}),(0,a.jsx)(D.MJ,{children:(0,a.jsx)(p.p,{type:"date",...t})}),(0,a.jsx)(D.C5,{})]})}}),(0,a.jsx)(D.zB,{control:Q.control,name:"location",render:e=>{let{field:t}=e;return(0,a.jsxs)(D.eI,{children:[(0,a.jsx)(D.lR,{children:"Location (Optional)"}),(0,a.jsx)(D.MJ,{children:(0,a.jsx)(p.p,{placeholder:"Location",...t})}),(0,a.jsx)(D.C5,{})]})}})]}),(0,a.jsx)(D.zB,{control:Q.control,name:"note",render:e=>{let{field:t}=e;return(0,a.jsxs)(D.eI,{children:[(0,a.jsx)(D.lR,{children:"Note (Optional)"}),(0,a.jsx)(D.MJ,{children:(0,a.jsx)(I,{placeholder:"Add any additional notes",className:"resize-none",...t})}),(0,a.jsx)(D.C5,{})]})}}),(0,a.jsx)(_,{children:(0,a.jsx)(m.$,{type:"submit",disabled:E,children:E?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(T.A,{className:"mr-2 h-4 w-4 animate-spin"}),Z?"Updating...":"Creating..."]}):Z?"Update Transaction":"Create Transaction"})})]})})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(O,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(p.p,{placeholder:"Search transactions...",className:"pl-9",value:$,onChange:e=>H(e.target.value)})]}),(0,a.jsxs)(k.l6,{value:K,onValueChange:X,children:[(0,a.jsx)(k.bq,{className:"w-full md:w-[180px]",children:(0,a.jsx)(k.yv,{placeholder:"Filter by type"})}),(0,a.jsxs)(k.gC,{children:[(0,a.jsx)(k.eb,{value:"all",children:"All Types"}),(0,a.jsx)(k.eb,{value:"income",children:"Income"}),(0,a.jsx)(k.eb,{value:"expense",children:"Expense"})]})]})]}),(0,a.jsxs)(j.Zp,{children:[(0,a.jsx)(j.aR,{children:(0,a.jsx)(j.ZB,{children:"Recent Transactions"})}),(0,a.jsx)(j.Wu,{children:b?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary"})}):0===er.length?(0,a.jsx)("div",{className:"text-center py-10",children:(0,a.jsx)("p",{className:"text-muted-foreground",children:"No transactions found"})}):(0,a.jsxs)(i,{children:[(0,a.jsx)(c,{children:(0,a.jsxs)(u,{children:[(0,a.jsx)(x,{children:"Date"}),(0,a.jsx)(x,{children:"Title"}),(0,a.jsx)(x,{children:"Category"}),(0,a.jsx)(x,{children:"Account"}),(0,a.jsx)(x,{className:"text-right",children:"Amount"}),(0,a.jsx)(x,{className:"text-right",children:"Actions"})]})}),(0,a.jsx)(d,{children:er.map(e=>(0,a.jsxs)(u,{children:[(0,a.jsx)(h,{children:(0,l.GP)(new Date(e.transaction_date),"MMM dd, yyyy")}),(0,a.jsx)(h,{children:e.title}),(0,a.jsx)(h,{children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{children:e.category.icon}),(0,a.jsx)("span",{children:e.category.name})]})}),(0,a.jsx)(h,{children:e.account.name}),(0,a.jsx)(h,{className:"text-right",children:(0,a.jsxs)("div",{className:"flex items-center justify-end gap-1 font-medium ".concat("income"===e.type?"text-green-600":"text-red-600"),children:["income"===e.type?(0,a.jsx)(V,{className:"h-4 w-4"}):(0,a.jsx)(B,{className:"h-4 w-4"}),ea(e.amount,e.currency)]})}),(0,a.jsx)(h,{className:"text-right",children:(0,a.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,a.jsxs)(m.$,{variant:"ghost",size:"icon",onClick:()=>et(e),children:[(0,a.jsx)(P,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Edit"})]}),(0,a.jsxs)(m.$,{variant:"ghost",size:"icon",className:"text-red-600",onClick:()=>en(e.id),children:[(0,a.jsx)(q,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Delete"})]})]})})]},e.id))})]})})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[464,204,671,550,268,433,319,171,511,441,684,358],()=>t(10814)),_N_E=e.O()}]);