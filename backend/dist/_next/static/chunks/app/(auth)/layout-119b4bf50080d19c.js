(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[97],{615:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>s});var r=o(95155),a=o(27600),n=o(89074);function s(e){let{children:t}=e;return(0,r.jsxs)(a.O,{children:[t,(0,r.jsx)(n.l,{})]})}},25731:(e,t,o)=>{"use strict";o.d(t,{A:()=>a});let r=o(23464).A.create({baseURL:"https://app.kolay-butce.com/api/v1",headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{{let t=localStorage.getItem("auth_token");t&&e.headers&&(e.headers.Authorization="Bearer ".concat(t))}return e},e=>Promise.reject(e)),r.interceptors.response.use(e=>(console.log("API Response:",e),e.data&&e.data.data)?e.data.data:e.data?e.data:(console.warn("Empty response data"),{}),e=>{var t,o,r;return console.error("API Error:",e),(null==(t=e.response)?void 0:t.status)===401&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"),"/login"!==window.location.pathname&&(window.location.href="/login")),Promise.reject((null==(r=e.response)||null==(o=r.data)?void 0:o.error)||e.message||"An unknown error occurred")});let a={auth:{login:e=>r.post("/login",e),register:e=>r.post("/register",e),getProfile:()=>r.get("/me"),logout:()=>r.post("/logout")},categories:{getAll:e=>r.get("/categories",{params:{type:e}}),getById:e=>r.get("/categories/".concat(e)),create:e=>r.post("/categories",e),update:(e,t)=>r.put("/categories/".concat(e),t),delete:e=>r.delete("/categories/".concat(e))},accounts:{getAll:()=>r.get("/accounts"),getById:e=>r.get("/accounts/".concat(e)),create:e=>r.post("/accounts",e),update:(e,t)=>r.put("/accounts/".concat(e),t),delete:e=>r.delete("/accounts/".concat(e))},transactions:{getAll:e=>r.get("/transactions",{params:e}),getById:e=>r.get("/transactions/".concat(e)),create:e=>r.post("/transactions",e),update:(e,t)=>r.put("/transactions/".concat(e),t),delete:e=>r.delete("/transactions/".concat(e))},reports:{getSummary:async e=>{try{let t=await r.get("/reports/summary",{params:e});if(!t||"object"!=typeof t)return console.warn("Invalid summary response, using default values"),{totalIncome:0,totalExpense:0,netBalance:0,topExpenseCategories:[],topIncomeCategories:[]};return t}catch(e){return console.error("Error fetching summary:",e),{totalIncome:0,totalExpense:0,netBalance:0,topExpenseCategories:[],topIncomeCategories:[]}}},getCategoryBreakdown:async e=>{try{let t=await r.get("/reports/category-breakdown",{params:e});if(!t||"object"!=typeof t)return{expenseCategories:[],incomeCategories:[]};return t}catch(e){return console.error("Error fetching category breakdown:",e),{expenseCategories:[],incomeCategories:[]}}},getMonthlyReport:async e=>{try{let t=await r.get("/reports/monthly",{params:e});if(!t||"object"!=typeof t)return{months:[]};return t}catch(e){return console.error("Error fetching monthly report:",e),{months:[]}}},getLocationSummary:async e=>{try{let t=await r.get("/reports/location-summary",{params:e});if(!t||"object"!=typeof t)return{locations:[]};return t}catch(e){return console.error("Error fetching location summary:",e),{locations:[]}}}},bankStatements:{upload:e=>r.post("/bank-statements/upload",e,{headers:{"Content-Type":"multipart/form-data"}}),import:e=>r.post("/bank-statements/import",e)}}},27600:(e,t,o)=>{"use strict";o.d(t,{A:()=>i,O:()=>l});var r=o(95155),a=o(12115),n=o(25731),s=o(35695);let c=(0,a.createContext)(void 0);function l(e){let{children:t}=e,[o,l]=(0,a.useState)(null),[i,u]=(0,a.useState)(!0),m=(0,s.useRouter)();(0,a.useEffect)(()=>{(async()=>{try{if(!localStorage.getItem("auth_token"))return void u(!1);let e=await n.A.auth.getProfile();l(e)}catch(e){localStorage.removeItem("auth_token"),localStorage.removeItem("user")}finally{u(!1)}})()},[]);let d=async(e,t)=>{u(!0);try{let o=await n.A.auth.login({username:e,password:t});localStorage.setItem("auth_token",o.token),localStorage.setItem("user",JSON.stringify(o.user)),l(o.user),m.push("/dashboard")}catch(e){throw e}finally{u(!1)}},g=async e=>{u(!0);try{let t=await n.A.auth.register(e);localStorage.setItem("auth_token",t.token),localStorage.setItem("user",JSON.stringify(t.user)),l(t.user),m.push("/dashboard")}catch(e){throw e}finally{u(!1)}},p=async()=>{u(!0);try{await n.A.auth.logout()}catch(e){console.error("Logout error:",e)}finally{localStorage.removeItem("auth_token"),localStorage.removeItem("user"),l(null),m.push("/login"),u(!1)}};return(0,r.jsx)(c.Provider,{value:{user:o,isLoading:i,isAuthenticated:!!o,login:d,register:g,logout:p},children:t})}function i(){let e=(0,a.useContext)(c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},32493:(e,t,o)=>{Promise.resolve().then(o.bind(o,615))},51362:(e,t,o)=>{"use strict";o.d(t,{D:()=>c});var r=o(12115),a="(prefers-color-scheme: dark)",n=r.createContext(void 0),s={setTheme:e=>{},themes:[]},c=()=>{var e;return null!=(e=r.useContext(n))?e:s},l=null,i=(e,t)=>{let o;try{o=localStorage.getItem(e)||void 0}catch(e){}return o||t},u=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},m=e=>(e||(e=window.matchMedia(a)),e.matches?"dark":"light")},89074:(e,t,o)=>{"use strict";o.d(t,{l:()=>s});var r=o(95155),a=o(51362),n=o(56671);let s=e=>{let{...t}=e,{theme:o="system"}=(0,a.D)();return(0,r.jsx)(n.l$,{theme:o,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...t})}}},e=>{var t=t=>e(e.s=t);e.O(0,[464,753,441,684,358],()=>t(32493)),_N_E=e.O()}]);