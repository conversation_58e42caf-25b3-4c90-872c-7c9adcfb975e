"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[183],{381:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(19946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},22436:(e,r,t)=>{var n=t(12115),o="function"==typeof Object.is?Object.is:function(e,r){return e===r&&(0!==e||1/e==1/r)||e!=e&&r!=r},a=n.useState,l=n.useEffect,u=n.useLayoutEffect,i=n.useDebugValue;function d(e){var r=e.getSnapshot;e=e.value;try{var t=r();return!o(e,t)}catch(e){return!0}}var s="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,r){return r()}:function(e,r){var t=r(),n=a({inst:{value:t,getSnapshot:r}}),o=n[0].inst,s=n[1];return u(function(){o.value=t,o.getSnapshot=r,d(o)&&s({inst:o})},[e,t,r]),l(function(){return d(o)&&s({inst:o}),e(function(){d(o)&&s({inst:o})})},[e]),i(t),t};r.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:s},34835:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(19946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},35695:(e,r,t)=>{var n=t(18999);t.o(n,"usePathname")&&t.d(r,{usePathname:function(){return n.usePathname}}),t.o(n,"useRouter")&&t.d(r,{useRouter:function(){return n.useRouter}})},46308:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(19946).A)("receipt",[["path",{d:"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1Z",key:"q3az6g"}],["path",{d:"M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8",key:"1h4pet"}],["path",{d:"M12 17.5v-11",key:"1jc1ny"}]])},48698:(e,r,t)=>{t.d(r,{UC:()=>eZ,q7:()=>eY,JU:()=>eW,ZL:()=>eX,bL:()=>ez,wv:()=>e$,l9:()=>eq});var n=t(12115),o=t(85185),a=t(6101),l=t(46081),u=t(5845),i=t(63655),d=t(37328),s=t(94315),c=t(19178),p=t(92293),f=t(25519),v=t(61285),m=t(35152),h=t(34378),g=t(28905),w=t(89196),y=t(99708),x=t(39033),b=t(38168),k=t(93795),M=t(95155),C=["Enter"," "],R=["ArrowUp","PageDown","End"],j=["ArrowDown","PageUp","Home",...R],D={ltr:[...C,"ArrowRight"],rtl:[...C,"ArrowLeft"]},A={ltr:["ArrowLeft"],rtl:["ArrowRight"]},_="Menu",[E,S,I]=(0,d.N)(_),[P,T]=(0,l.A)(_,[I,m.Bk,w.RG]),L=(0,m.Bk)(),N=(0,w.RG)(),[F,O]=P(_),[G,K]=P(_),V=e=>{let{__scopeMenu:r,open:t=!1,children:o,dir:a,onOpenChange:l,modal:u=!0}=e,i=L(r),[d,c]=n.useState(null),p=n.useRef(!1),f=(0,x.c)(l),v=(0,s.jH)(a);return n.useEffect(()=>{let e=()=>{p.current=!0,document.addEventListener("pointerdown",r,{capture:!0,once:!0}),document.addEventListener("pointermove",r,{capture:!0,once:!0})},r=()=>p.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",r,{capture:!0}),document.removeEventListener("pointermove",r,{capture:!0})}},[]),(0,M.jsx)(m.bL,{...i,children:(0,M.jsx)(F,{scope:r,open:t,onOpenChange:f,content:d,onContentChange:c,children:(0,M.jsx)(G,{scope:r,onClose:n.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:p,dir:v,modal:u,children:o})})})};V.displayName=_;var H=n.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e,o=L(t);return(0,M.jsx)(m.Mz,{...o,...n,ref:r})});H.displayName="MenuAnchor";var B="MenuPortal",[U,z]=P(B,{forceMount:void 0}),q=e=>{let{__scopeMenu:r,forceMount:t,children:n,container:o}=e,a=O(B,r);return(0,M.jsx)(U,{scope:r,forceMount:t,children:(0,M.jsx)(g.C,{present:t||a.open,children:(0,M.jsx)(h.Z,{asChild:!0,container:o,children:n})})})};q.displayName=B;var X="MenuContent",[Z,W]=P(X),Y=n.forwardRef((e,r)=>{let t=z(X,e.__scopeMenu),{forceMount:n=t.forceMount,...o}=e,a=O(X,e.__scopeMenu),l=K(X,e.__scopeMenu);return(0,M.jsx)(E.Provider,{scope:e.__scopeMenu,children:(0,M.jsx)(g.C,{present:n||a.open,children:(0,M.jsx)(E.Slot,{scope:e.__scopeMenu,children:l.modal?(0,M.jsx)($,{...o,ref:r}):(0,M.jsx)(J,{...o,ref:r})})})})}),$=n.forwardRef((e,r)=>{let t=O(X,e.__scopeMenu),l=n.useRef(null),u=(0,a.s)(r,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,b.Eq)(e)},[]),(0,M.jsx)(ee,{...e,ref:u,trapFocus:t.open,disableOutsidePointerEvents:t.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>t.onOpenChange(!1)})}),J=n.forwardRef((e,r)=>{let t=O(X,e.__scopeMenu);return(0,M.jsx)(ee,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>t.onOpenChange(!1)})}),Q=(0,y.TL)("MenuContent.ScrollLock"),ee=n.forwardRef((e,r)=>{let{__scopeMenu:t,loop:l=!1,trapFocus:u,onOpenAutoFocus:i,onCloseAutoFocus:d,disableOutsidePointerEvents:s,onEntryFocus:v,onEscapeKeyDown:h,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:x,onDismiss:b,disableOutsideScroll:C,...D}=e,A=O(X,t),_=K(X,t),E=L(t),I=N(t),P=S(t),[T,F]=n.useState(null),G=n.useRef(null),V=(0,a.s)(r,G,A.onContentChange),H=n.useRef(0),B=n.useRef(""),U=n.useRef(0),z=n.useRef(null),q=n.useRef("right"),W=n.useRef(0),Y=C?k.A:n.Fragment,$=e=>{var r,t;let n=B.current+e,o=P().filter(e=>!e.disabled),a=document.activeElement,l=null==(r=o.find(e=>e.ref.current===a))?void 0:r.textValue,u=function(e,r,t){var n;let o=r.length>1&&Array.from(r).every(e=>e===r[0])?r[0]:r,a=t?e.indexOf(t):-1,l=(n=Math.max(a,0),e.map((r,t)=>e[(n+t)%e.length]));1===o.length&&(l=l.filter(e=>e!==t));let u=l.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return u!==t?u:void 0}(o.map(e=>e.textValue),n,l),i=null==(t=o.find(e=>e.textValue===u))?void 0:t.ref.current;!function e(r){B.current=r,window.clearTimeout(H.current),""!==r&&(H.current=window.setTimeout(()=>e(""),1e3))}(n),i&&setTimeout(()=>i.focus())};n.useEffect(()=>()=>window.clearTimeout(H.current),[]),(0,p.Oh)();let J=n.useCallback(e=>{var r,t;return q.current===(null==(r=z.current)?void 0:r.side)&&function(e,r){return!!r&&function(e,r){let{x:t,y:n}=e,o=!1;for(let e=0,a=r.length-1;e<r.length;a=e++){let l=r[e],u=r[a],i=l.x,d=l.y,s=u.x,c=u.y;d>n!=c>n&&t<(s-i)*(n-d)/(c-d)+i&&(o=!o)}return o}({x:e.clientX,y:e.clientY},r)}(e,null==(t=z.current)?void 0:t.area)},[]);return(0,M.jsx)(Z,{scope:t,searchRef:B,onItemEnter:n.useCallback(e=>{J(e)&&e.preventDefault()},[J]),onItemLeave:n.useCallback(e=>{var r;J(e)||(null==(r=G.current)||r.focus(),F(null))},[J]),onTriggerLeave:n.useCallback(e=>{J(e)&&e.preventDefault()},[J]),pointerGraceTimerRef:U,onPointerGraceIntentChange:n.useCallback(e=>{z.current=e},[]),children:(0,M.jsx)(Y,{...C?{as:Q,allowPinchZoom:!0}:void 0,children:(0,M.jsx)(f.n,{asChild:!0,trapped:u,onMountAutoFocus:(0,o.m)(i,e=>{var r;e.preventDefault(),null==(r=G.current)||r.focus({preventScroll:!0})}),onUnmountAutoFocus:d,children:(0,M.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:h,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:x,onDismiss:b,children:(0,M.jsx)(w.bL,{asChild:!0,...I,dir:_.dir,orientation:"vertical",loop:l,currentTabStopId:T,onCurrentTabStopIdChange:F,onEntryFocus:(0,o.m)(v,e=>{_.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,M.jsx)(m.UC,{role:"menu","aria-orientation":"vertical","data-state":ej(A.open),"data-radix-menu-content":"",dir:_.dir,...E,...D,ref:V,style:{outline:"none",...D.style},onKeyDown:(0,o.m)(D.onKeyDown,e=>{let r=e.target.closest("[data-radix-menu-content]")===e.currentTarget,t=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;r&&("Tab"===e.key&&e.preventDefault(),!t&&n&&$(e.key));let o=G.current;if(e.target!==o||!j.includes(e.key))return;e.preventDefault();let a=P().filter(e=>!e.disabled).map(e=>e.ref.current);R.includes(e.key)&&a.reverse(),function(e){let r=document.activeElement;for(let t of e)if(t===r||(t.focus(),document.activeElement!==r))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(H.current),B.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,e_(e=>{let r=e.target,t=W.current!==e.clientX;e.currentTarget.contains(r)&&t&&(q.current=e.clientX>W.current?"right":"left",W.current=e.clientX)}))})})})})})})});Y.displayName=X;var er=n.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e;return(0,M.jsx)(i.sG.div,{role:"group",...n,ref:r})});er.displayName="MenuGroup";var et=n.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e;return(0,M.jsx)(i.sG.div,{...n,ref:r})});et.displayName="MenuLabel";var en="MenuItem",eo="menu.itemSelect",ea=n.forwardRef((e,r)=>{let{disabled:t=!1,onSelect:l,...u}=e,d=n.useRef(null),s=K(en,e.__scopeMenu),c=W(en,e.__scopeMenu),p=(0,a.s)(r,d),f=n.useRef(!1);return(0,M.jsx)(el,{...u,ref:p,disabled:t,onClick:(0,o.m)(e.onClick,()=>{let e=d.current;if(!t&&e){let r=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>null==l?void 0:l(e),{once:!0}),(0,i.hO)(e,r),r.defaultPrevented?f.current=!1:s.onClose()}}),onPointerDown:r=>{var t;null==(t=e.onPointerDown)||t.call(e,r),f.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var r;f.current||null==(r=e.currentTarget)||r.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=""!==c.searchRef.current;t||r&&" "===e.key||C.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ea.displayName=en;var el=n.forwardRef((e,r)=>{let{__scopeMenu:t,disabled:l=!1,textValue:u,...d}=e,s=W(en,t),c=N(t),p=n.useRef(null),f=(0,a.s)(r,p),[v,m]=n.useState(!1),[h,g]=n.useState("");return n.useEffect(()=>{let e=p.current;if(e){var r;g((null!=(r=e.textContent)?r:"").trim())}},[d.children]),(0,M.jsx)(E.ItemSlot,{scope:t,disabled:l,textValue:null!=u?u:h,children:(0,M.jsx)(w.q7,{asChild:!0,...c,focusable:!l,children:(0,M.jsx)(i.sG.div,{role:"menuitem","data-highlighted":v?"":void 0,"aria-disabled":l||void 0,"data-disabled":l?"":void 0,...d,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,e_(e=>{l?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,e_(e=>s.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>m(!0)),onBlur:(0,o.m)(e.onBlur,()=>m(!1))})})})}),eu=n.forwardRef((e,r)=>{let{checked:t=!1,onCheckedChange:n,...a}=e;return(0,M.jsx)(em,{scope:e.__scopeMenu,checked:t,children:(0,M.jsx)(ea,{role:"menuitemcheckbox","aria-checked":eD(t)?"mixed":t,...a,ref:r,"data-state":eA(t),onSelect:(0,o.m)(a.onSelect,()=>null==n?void 0:n(!!eD(t)||!t),{checkForDefaultPrevented:!1})})})});eu.displayName="MenuCheckboxItem";var ei="MenuRadioGroup",[ed,es]=P(ei,{value:void 0,onValueChange:()=>{}}),ec=n.forwardRef((e,r)=>{let{value:t,onValueChange:n,...o}=e,a=(0,x.c)(n);return(0,M.jsx)(ed,{scope:e.__scopeMenu,value:t,onValueChange:a,children:(0,M.jsx)(er,{...o,ref:r})})});ec.displayName=ei;var ep="MenuRadioItem",ef=n.forwardRef((e,r)=>{let{value:t,...n}=e,a=es(ep,e.__scopeMenu),l=t===a.value;return(0,M.jsx)(em,{scope:e.__scopeMenu,checked:l,children:(0,M.jsx)(ea,{role:"menuitemradio","aria-checked":l,...n,ref:r,"data-state":eA(l),onSelect:(0,o.m)(n.onSelect,()=>{var e;return null==(e=a.onValueChange)?void 0:e.call(a,t)},{checkForDefaultPrevented:!1})})})});ef.displayName=ep;var ev="MenuItemIndicator",[em,eh]=P(ev,{checked:!1}),eg=n.forwardRef((e,r)=>{let{__scopeMenu:t,forceMount:n,...o}=e,a=eh(ev,t);return(0,M.jsx)(g.C,{present:n||eD(a.checked)||!0===a.checked,children:(0,M.jsx)(i.sG.span,{...o,ref:r,"data-state":eA(a.checked)})})});eg.displayName=ev;var ew=n.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e;return(0,M.jsx)(i.sG.div,{role:"separator","aria-orientation":"horizontal",...n,ref:r})});ew.displayName="MenuSeparator";var ey=n.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e,o=L(t);return(0,M.jsx)(m.i3,{...o,...n,ref:r})});ey.displayName="MenuArrow";var[ex,eb]=P("MenuSub"),ek="MenuSubTrigger",eM=n.forwardRef((e,r)=>{let t=O(ek,e.__scopeMenu),l=K(ek,e.__scopeMenu),u=eb(ek,e.__scopeMenu),i=W(ek,e.__scopeMenu),d=n.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:c}=i,p={__scopeMenu:e.__scopeMenu},f=n.useCallback(()=>{d.current&&window.clearTimeout(d.current),d.current=null},[]);return n.useEffect(()=>f,[f]),n.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),c(null)}},[s,c]),(0,M.jsx)(H,{asChild:!0,...p,children:(0,M.jsx)(el,{id:u.triggerId,"aria-haspopup":"menu","aria-expanded":t.open,"aria-controls":u.contentId,"data-state":ej(t.open),...e,ref:(0,a.t)(r,u.onTriggerChange),onClick:r=>{var n;null==(n=e.onClick)||n.call(e,r),e.disabled||r.defaultPrevented||(r.currentTarget.focus(),t.open||t.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,e_(r=>{i.onItemEnter(r),!r.defaultPrevented&&(e.disabled||t.open||d.current||(i.onPointerGraceIntentChange(null),d.current=window.setTimeout(()=>{t.onOpenChange(!0),f()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,e_(e=>{var r,n;f();let o=null==(r=t.content)?void 0:r.getBoundingClientRect();if(o){let r=null==(n=t.content)?void 0:n.dataset.side,a="right"===r,l=o[a?"left":"right"],u=o[a?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:l,y:o.top},{x:u,y:o.top},{x:u,y:o.bottom},{x:l,y:o.bottom}],side:r}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,r=>{let n=""!==i.searchRef.current;if(!e.disabled&&(!n||" "!==r.key)&&D[l.dir].includes(r.key)){var o;t.onOpenChange(!0),null==(o=t.content)||o.focus(),r.preventDefault()}})})})});eM.displayName=ek;var eC="MenuSubContent",eR=n.forwardRef((e,r)=>{let t=z(X,e.__scopeMenu),{forceMount:l=t.forceMount,...u}=e,i=O(X,e.__scopeMenu),d=K(X,e.__scopeMenu),s=eb(eC,e.__scopeMenu),c=n.useRef(null),p=(0,a.s)(r,c);return(0,M.jsx)(E.Provider,{scope:e.__scopeMenu,children:(0,M.jsx)(g.C,{present:l||i.open,children:(0,M.jsx)(E.Slot,{scope:e.__scopeMenu,children:(0,M.jsx)(ee,{id:s.contentId,"aria-labelledby":s.triggerId,...u,ref:p,align:"start",side:"rtl"===d.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var r;d.isUsingKeyboardRef.current&&(null==(r=c.current)||r.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==s.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{d.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=e.currentTarget.contains(e.target),t=A[d.dir].includes(e.key);if(r&&t){var n;i.onOpenChange(!1),null==(n=s.trigger)||n.focus(),e.preventDefault()}})})})})})});function ej(e){return e?"open":"closed"}function eD(e){return"indeterminate"===e}function eA(e){return eD(e)?"indeterminate":e?"checked":"unchecked"}function e_(e){return r=>"mouse"===r.pointerType?e(r):void 0}eR.displayName=eC;var eE="DropdownMenu",[eS,eI]=(0,l.A)(eE,[T]),eP=T(),[eT,eL]=eS(eE),eN=e=>{let{__scopeDropdownMenu:r,children:t,dir:o,open:a,defaultOpen:l,onOpenChange:i,modal:d=!0}=e,s=eP(r),c=n.useRef(null),[p,f]=(0,u.i)({prop:a,defaultProp:null!=l&&l,onChange:i,caller:eE});return(0,M.jsx)(eT,{scope:r,triggerId:(0,v.B)(),triggerRef:c,contentId:(0,v.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:d,children:(0,M.jsx)(V,{...s,open:p,onOpenChange:f,dir:o,modal:d,children:t})})};eN.displayName=eE;var eF="DropdownMenuTrigger",eO=n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,disabled:n=!1,...l}=e,u=eL(eF,t),d=eP(t);return(0,M.jsx)(H,{asChild:!0,...d,children:(0,M.jsx)(i.sG.button,{type:"button",id:u.triggerId,"aria-haspopup":"menu","aria-expanded":u.open,"aria-controls":u.open?u.contentId:void 0,"data-state":u.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...l,ref:(0,a.t)(r,u.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!n&&0===e.button&&!1===e.ctrlKey&&(u.onOpenToggle(),u.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&u.onOpenToggle(),"ArrowDown"===e.key&&u.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eO.displayName=eF;var eG=e=>{let{__scopeDropdownMenu:r,...t}=e,n=eP(r);return(0,M.jsx)(q,{...n,...t})};eG.displayName="DropdownMenuPortal";var eK="DropdownMenuContent",eV=n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...a}=e,l=eL(eK,t),u=eP(t),i=n.useRef(!1);return(0,M.jsx)(Y,{id:l.contentId,"aria-labelledby":l.triggerId,...u,...a,ref:r,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var r;i.current||null==(r=l.triggerRef.current)||r.focus(),i.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let r=e.detail.originalEvent,t=0===r.button&&!0===r.ctrlKey,n=2===r.button||t;(!l.modal||n)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eV.displayName=eK,n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eP(t);return(0,M.jsx)(er,{...o,...n,ref:r})}).displayName="DropdownMenuGroup";var eH=n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eP(t);return(0,M.jsx)(et,{...o,...n,ref:r})});eH.displayName="DropdownMenuLabel";var eB=n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eP(t);return(0,M.jsx)(ea,{...o,...n,ref:r})});eB.displayName="DropdownMenuItem",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eP(t);return(0,M.jsx)(eu,{...o,...n,ref:r})}).displayName="DropdownMenuCheckboxItem",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eP(t);return(0,M.jsx)(ec,{...o,...n,ref:r})}).displayName="DropdownMenuRadioGroup",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eP(t);return(0,M.jsx)(ef,{...o,...n,ref:r})}).displayName="DropdownMenuRadioItem",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eP(t);return(0,M.jsx)(eg,{...o,...n,ref:r})}).displayName="DropdownMenuItemIndicator";var eU=n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eP(t);return(0,M.jsx)(ew,{...o,...n,ref:r})});eU.displayName="DropdownMenuSeparator",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eP(t);return(0,M.jsx)(ey,{...o,...n,ref:r})}).displayName="DropdownMenuArrow",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eP(t);return(0,M.jsx)(eM,{...o,...n,ref:r})}).displayName="DropdownMenuSubTrigger",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eP(t);return(0,M.jsx)(eR,{...o,...n,ref:r,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var ez=eN,eq=eO,eX=eG,eZ=eV,eW=eH,eY=eB,e$=eU},49033:(e,r,t)=>{e.exports=t(22436)},51362:(e,r,t)=>{t.d(r,{D:()=>u});var n=t(12115),o="(prefers-color-scheme: dark)",a=n.createContext(void 0),l={setTheme:e=>{},themes:[]},u=()=>{var e;return null!=(e=n.useContext(a))?e:l},i=null,d=(e,r)=>{let t;try{t=localStorage.getItem(e)||void 0}catch(e){}return t||r},s=e=>{let r=document.createElement("style");return e&&r.setAttribute("nonce",e),r.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(r),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(r)},1)}},c=e=>(e||(e=window.matchMedia(o)),e.matches?"dark":"light")},53817:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(19946).A)("tags",[["path",{d:"m15 5 6.3 6.3a2.4 2.4 0 0 1 0 3.4L17 19",key:"1cbfv1"}],["path",{d:"M9.586 5.586A2 2 0 0 0 8.172 5H3a1 1 0 0 0-1 1v5.172a2 2 0 0 0 .586 1.414L8.29 18.29a2.426 2.426 0 0 0 3.42 0l3.58-3.58a2.426 2.426 0 0 0 0-3.42z",key:"135mg7"}],["circle",{cx:"6.5",cy:"9.5",r:".5",fill:"currentColor",key:"5pm5xn"}]])},54011:(e,r,t)=>{t.d(r,{H4:()=>C,_V:()=>M,bL:()=>k});var n=t(12115),o=t(46081),a=t(39033),l=t(52712),u=t(63655),i=t(49033);function d(){return()=>{}}var s=t(95155),c="Avatar",[p,f]=(0,o.A)(c),[v,m]=p(c),h=n.forwardRef((e,r)=>{let{__scopeAvatar:t,...o}=e,[a,l]=n.useState("idle");return(0,s.jsx)(v,{scope:t,imageLoadingStatus:a,onImageLoadingStatusChange:l,children:(0,s.jsx)(u.sG.span,{...o,ref:r})})});h.displayName=c;var g="AvatarImage",w=n.forwardRef((e,r)=>{let{__scopeAvatar:t,src:o,onLoadingStatusChange:c=()=>{},...p}=e,f=m(g,t),v=function(e,r){let{referrerPolicy:t,crossOrigin:o}=r,a=(0,i.useSyncExternalStore)(d,()=>!0,()=>!1),u=n.useRef(null),s=a?(u.current||(u.current=new window.Image),u.current):null,[c,p]=n.useState(()=>b(s,e));return(0,l.N)(()=>{p(b(s,e))},[s,e]),(0,l.N)(()=>{let e=e=>()=>{p(e)};if(!s)return;let r=e("loaded"),n=e("error");return s.addEventListener("load",r),s.addEventListener("error",n),t&&(s.referrerPolicy=t),"string"==typeof o&&(s.crossOrigin=o),()=>{s.removeEventListener("load",r),s.removeEventListener("error",n)}},[s,o,t]),c}(o,p),h=(0,a.c)(e=>{c(e),f.onImageLoadingStatusChange(e)});return(0,l.N)(()=>{"idle"!==v&&h(v)},[v,h]),"loaded"===v?(0,s.jsx)(u.sG.img,{...p,ref:r,src:o}):null});w.displayName=g;var y="AvatarFallback",x=n.forwardRef((e,r)=>{let{__scopeAvatar:t,delayMs:o,...a}=e,l=m(y,t),[i,d]=n.useState(void 0===o);return n.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>d(!0),o);return()=>window.clearTimeout(e)}},[o]),i&&"loaded"!==l.imageLoadingStatus?(0,s.jsx)(u.sG.span,{...a,ref:r}):null});function b(e,r){return e?r?(e.src!==r&&(e.src=r),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}x.displayName=y;var k=h,M=w,C=x},57434:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},71007:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},73783:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(19946).A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},74466:(e,r,t)=>{t.d(r,{F:()=>l});var n=t(52596);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=n.$,l=(e,r)=>t=>{var n;if((null==r?void 0:r.variants)==null)return a(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:l,defaultVariants:u}=r,i=Object.keys(l).map(e=>{let r=null==t?void 0:t[e],n=null==u?void 0:u[e];if(null===r)return null;let a=o(r)||o(n);return l[e][a]}),d=t&&Object.entries(t).reduce((e,r)=>{let[t,n]=r;return void 0===n||(e[t]=n),e},{});return a(e,i,null==r||null==(n=r.compoundVariants)?void 0:n.reduce((e,r)=>{let{class:t,className:n,...o}=r;return Object.entries(o).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...u,...d}[r]):({...u,...d})[r]===t})?[...e,t,n]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},74783:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(19946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},81586:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(19946).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},89196:(e,r,t)=>{t.d(r,{RG:()=>b,bL:()=>E,q7:()=>S});var n=t(12115),o=t(85185),a=t(37328),l=t(6101),u=t(46081),i=t(61285),d=t(63655),s=t(39033),c=t(5845),p=t(94315),f=t(95155),v="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[g,w,y]=(0,a.N)(h),[x,b]=(0,u.A)(h,[y]),[k,M]=x(h),C=n.forwardRef((e,r)=>(0,f.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(R,{...e,ref:r})})}));C.displayName=h;var R=n.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,orientation:a,loop:u=!1,dir:i,currentTabStopId:g,defaultCurrentTabStopId:y,onCurrentTabStopIdChange:x,onEntryFocus:b,preventScrollOnEntryFocus:M=!1,...C}=e,R=n.useRef(null),j=(0,l.s)(r,R),D=(0,p.jH)(i),[A,E]=(0,c.i)({prop:g,defaultProp:null!=y?y:null,onChange:x,caller:h}),[S,I]=n.useState(!1),P=(0,s.c)(b),T=w(t),L=n.useRef(!1),[N,F]=n.useState(0);return n.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(v,P),()=>e.removeEventListener(v,P)},[P]),(0,f.jsx)(k,{scope:t,orientation:a,dir:D,loop:u,currentTabStopId:A,onItemFocus:n.useCallback(e=>E(e),[E]),onItemShiftTab:n.useCallback(()=>I(!0),[]),onFocusableItemAdd:n.useCallback(()=>F(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>F(e=>e-1),[]),children:(0,f.jsx)(d.sG.div,{tabIndex:S||0===N?-1:0,"data-orientation":a,...C,ref:j,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{L.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let r=!L.current;if(e.target===e.currentTarget&&r&&!S){let r=new CustomEvent(v,m);if(e.currentTarget.dispatchEvent(r),!r.defaultPrevented){let e=T().filter(e=>e.focusable);_([e.find(e=>e.active),e.find(e=>e.id===A),...e].filter(Boolean).map(e=>e.ref.current),M)}}L.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>I(!1))})})}),j="RovingFocusGroupItem",D=n.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,focusable:a=!0,active:l=!1,tabStopId:u,children:s,...c}=e,p=(0,i.B)(),v=u||p,m=M(j,t),h=m.currentTabStopId===v,y=w(t),{onFocusableItemAdd:x,onFocusableItemRemove:b,currentTabStopId:k}=m;return n.useEffect(()=>{if(a)return x(),()=>b()},[a,x,b]),(0,f.jsx)(g.ItemSlot,{scope:t,id:v,focusable:a,active:l,children:(0,f.jsx)(d.sG.span,{tabIndex:h?0:-1,"data-orientation":m.orientation,...c,ref:r,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?m.onItemFocus(v):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>m.onItemFocus(v)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void m.onItemShiftTab();if(e.target!==e.currentTarget)return;let r=function(e,r,t){var n;let o=(n=e.key,"rtl"!==t?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===r&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===r&&["ArrowUp","ArrowDown"].includes(o)))return A[o]}(e,m.orientation,m.dir);if(void 0!==r){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let t=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===r)t.reverse();else if("prev"===r||"next"===r){"prev"===r&&t.reverse();let n=t.indexOf(e.currentTarget);t=m.loop?function(e,r){return e.map((t,n)=>e[(r+n)%e.length])}(t,n+1):t.slice(n+1)}setTimeout(()=>_(t))}}),children:"function"==typeof s?s({isCurrentTabStop:h,hasTabStop:null!=k}):s})})});D.displayName=j;var A={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function _(e){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=document.activeElement;for(let n of e)if(n===t||(n.focus({preventScroll:r}),document.activeElement!==t))return}var E=C,S=D}}]);