import 'package:flutter/foundation.dart';
import 'package:fin_notebook/models/transaction.dart';
import 'package:fin_notebook/services/transaction_service.dart';
import 'package:fin_notebook/utils/logger.dart';

class TransactionProvider with ChangeNotifier {
  final TransactionService _transactionService = TransactionService();
  List<FinTransaction> _transactions = [];
  bool _isLoading = false;

  List<FinTransaction> get transactions => _transactions;

  // Clear all transactions (used when user logs out)
  void clearTransactions() {
    _transactions = [];
    notifyListeners();
  }
  bool get isLoading => _isLoading;

  List<FinTransaction> get incomeTransactions =>
      _transactions.where((transaction) => transaction.type == 'income').toList();

  List<FinTransaction> get expenseTransactions =>
      _transactions.where((transaction) => transaction.type == 'expense').toList();

  double get totalIncome => incomeTransactions.fold(
      0, (sum, transaction) => sum + transaction.amount);

  double get totalExpense => expenseTransactions.fold(
      0, (sum, transaction) => sum + transaction.amount);

  double get balance => totalIncome - totalExpense;

  Future<void> loadTransactions({
    String? type,
    String? categoryId,
    String? accountId,
    DateTime? startDate,
    DateTime? endDate,
    String? paymentMethod,
    double? minAmount,
    double? maxAmount,
    String? search,
  }) async {
    _isLoading = true;
    notifyListeners();

    try {
      final response = await _transactionService.getTransactions(
        type: type,
        categoryId: categoryId,
        accountId: accountId,
        startDate: startDate,
        endDate: endDate,
        paymentMethod: paymentMethod,
        minAmount: minAmount,
        maxAmount: maxAmount,
        search: search,
      );

      _transactions = response.map((data) => FinTransaction.fromMap(data)).toList();
    } catch (e) {
      AppLogger.error('Error loading transactions: $e', e);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> addTransaction({
    required String title,
    required String type,
    required double amount,
    required String currency,
    required String categoryId,
    required String paymentMethod,
    required String accountId,
    String? note,
    required DateTime transactionDate,
    String? location,
  }) async {
    try {
      final response = await _transactionService.createTransaction(
        title: title,
        type: type,
        amount: amount,
        currency: currency,
        categoryId: categoryId,
        paymentMethod: paymentMethod,
        accountId: accountId,
        note: note,
        transactionDate: transactionDate,
        location: location,
      );

      final newTransaction = FinTransaction.fromMap(response);
      _transactions.add(newTransaction);
      _transactions.sort((a, b) => b.transactionDate.compareTo(a.transactionDate)); // Sort by date descending
      notifyListeners();
    } catch (e) {
      AppLogger.error('Error adding transaction: $e', e);
    }
  }

  Future<void> addTransactionObject(FinTransaction transaction) async {
    return addTransaction(
      title: transaction.title,
      type: transaction.type,
      amount: transaction.amount,
      currency: transaction.currency,
      categoryId: transaction.categoryId,
      paymentMethod: transaction.paymentMethod,
      accountId: transaction.accountId,
      note: transaction.note,
      transactionDate: transaction.transactionDate,
      location: transaction.location,
    );
  }

  Future<void> updateTransaction(FinTransaction transaction) async {
    try {
      if (transaction.id == null) {
        throw Exception('Transaction ID cannot be null for update operation');
      }

      final response = await _transactionService.updateTransaction(
        id: transaction.id!,
        title: transaction.title,
        type: transaction.type,
        amount: transaction.amount,
        currency: transaction.currency,
        categoryId: transaction.categoryId,
        paymentMethod: transaction.paymentMethod,
        accountId: transaction.accountId,
        note: transaction.note,
        transactionDate: transaction.transactionDate,
        location: transaction.location,
      );

      final updatedTransaction = FinTransaction.fromMap(response);
      final index = _transactions.indexWhere((t) => t.id == transaction.id);
      if (index != -1) {
        _transactions[index] = updatedTransaction;
        _transactions.sort((a, b) => b.transactionDate.compareTo(a.transactionDate)); // Sort by date descending
        notifyListeners();
      }
    } catch (e) {
      AppLogger.error('Error updating transaction: $e', e);
    }
  }

  Future<void> deleteTransaction(String id) async {
    try {
      await _transactionService.deleteTransaction(id);
      _transactions.removeWhere((transaction) => transaction.id == id);
      notifyListeners();
    } catch (e) {
      AppLogger.error('Error deleting transaction: $e', e);
    }
  }

  List<FinTransaction> getTransactionsByCategory(String categoryId) {
    return _transactions.where((t) => t.categoryId == categoryId).toList();
  }

  List<FinTransaction> getTransactionsByAccount(String accountId) {
    return _transactions.where((t) => t.accountId == accountId).toList();
  }

  List<FinTransaction> getTransactionsByDateRange(DateTime startDate, DateTime endDate) {
    return _transactions.where((t) =>
      t.transactionDate.isAfter(startDate.subtract(const Duration(days: 1))) &&
      t.transactionDate.isBefore(endDate.add(const Duration(days: 1)))
    ).toList();
  }

  Map<String, double> getCategoryTotals(String type) {
    final Map<String, double> categoryTotals = {};
    final typeTransactions = type == 'income' ? incomeTransactions : expenseTransactions;

    for (var transaction in typeTransactions) {
      final categoryId = transaction.categoryId;
      if (categoryTotals.containsKey(categoryId)) {
        categoryTotals[categoryId] =
            (categoryTotals[categoryId] ?? 0) + transaction.amount;
      } else {
        categoryTotals[categoryId] = transaction.amount;
      }
    }

    return categoryTotals;
  }
}
