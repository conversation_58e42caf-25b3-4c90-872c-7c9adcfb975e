import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fin_notebook/providers/transaction_provider.dart';
import 'package:fin_notebook/providers/category_provider.dart';
import 'package:fin_notebook/providers/account_provider.dart';
import 'package:fin_notebook/providers/auth_provider.dart';
import 'package:fin_notebook/widgets/summary_card.dart';
import 'package:fin_notebook/widgets/transaction_card.dart';
import 'package:fin_notebook/widgets/chart_widget.dart';
import 'package:fin_notebook/screens/add_edit_transaction_screen.dart';
import 'package:fin_notebook/screens/bank_statement_screen.dart';
import 'package:fin_notebook/screens/transaction_list_screen.dart';
import 'package:fin_notebook/utils/date_formatter.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  DateTime _selectedDate = DateTime.now();
  bool _isLoading = true;

  String? _currentUserId;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Check if user has changed
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    if (_currentUserId != authProvider.userId) {
      _currentUserId = authProvider.userId;
      // User has changed, reload data
      _loadData();
    }
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    final startDate = DateFormatter.getStartOfMonth(_selectedDate);
    final endDate = DateFormatter.getEndOfMonth(_selectedDate);

    try {
      // Load categories and accounts first
      final categoryProvider = Provider.of<CategoryProvider>(context, listen: false);
      final accountProvider = Provider.of<AccountProvider>(context, listen: false);
      final transactionProvider = Provider.of<TransactionProvider>(context, listen: false);

      if (mounted) {
        await categoryProvider.loadCategories();
        await accountProvider.loadAccounts();

        // Then load transactions for the selected month
        await transactionProvider.loadTransactions(
          startDate: startDate,
          endDate: endDate,
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading data: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _selectPreviousMonth() {
    setState(() {
      _selectedDate = DateTime(_selectedDate.year, _selectedDate.month - 1);
    });
    _loadData();
  }

  void _selectNextMonth() {
    setState(() {
      _selectedDate = DateTime(_selectedDate.year, _selectedDate.month + 1);
    });
    _loadData();
  }

  @override
  Widget build(BuildContext context) {
    final transactionProvider = Provider.of<TransactionProvider>(context);

    final income = transactionProvider.totalIncome;
    final expense = transactionProvider.totalExpense;
    final balance = transactionProvider.balance;

    final recentTransactions = transactionProvider.transactions.take(5).toList();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Finance Notebook'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Month selector
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          IconButton(
                            icon: const Icon(Icons.chevron_left),
                            onPressed: _selectPreviousMonth,
                          ),
                          Text(
                            DateFormatter.formatMonth(_selectedDate),
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.chevron_right),
                            onPressed: _selectNextMonth,
                          ),
                        ],
                      ),
                    ),

                    // Summary card
                    SummaryCard(
                      income: income,
                      expense: expense,
                      balance: balance,
                      period: DateFormatter.formatMonth(_selectedDate),
                    ),

                    // Charts
                    if (transactionProvider.expenseTransactions.isNotEmpty)
                      PieChartWidget(
                        categoryTotals: transactionProvider.getCategoryTotals('expense'),
                        type: 'Expenses',
                        total: expense,
                      ),

                    // Recent transactions
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'Recent Transactions',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          TextButton(
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const TransactionListScreen(),
                                ),
                              );
                            },
                            child: const Text('See All'),
                          ),
                        ],
                      ),
                    ),

                    if (recentTransactions.isEmpty)
                      const Padding(
                        padding: EdgeInsets.all(16),
                        child: Center(
                          child: Text('No transactions yet. Add your first one!'),
                        ),
                      )
                    else
                      ...recentTransactions.map((transaction) => TransactionCard(
                        transaction: transaction,
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => AddEditTransactionScreen(
                                transaction: transaction,
                              ),
                            ),
                          ).then((_) => _loadData());
                        },
                      )),

                    const SizedBox(height: 80), // Space for FAB
                  ],
                ),
              ),
            ),
      floatingActionButton: Padding(
        padding: const EdgeInsets.only(right: 80),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            // Bank Statement Import Button
            FloatingActionButton.extended(
              heroTag: 'bankStatement',
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const BankStatementScreen(),
                  ),
                ).then((_) => _loadData());
              },
              icon: const Icon(Icons.receipt_long),
              label: const Text('Import Statement'),
              backgroundColor: Colors.amber,
            ),
            const SizedBox(width: 16),
            // Add Transaction Button
            FloatingActionButton.extended(
              heroTag: 'addTransaction',
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AddEditTransactionScreen(),
                  ),
                ).then((_) => _loadData());
              },
              icon: const Icon(Icons.add),
              label: const Text('Add Transaction'),
            ),
          ],
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }
}
