import 'package:flutter/services.dart';

/// A TextInputFormatter that ensures proper handling of Turkish characters.
/// This formatter specifically addresses the issue where 'ı' (dotless i) 
/// is incorrectly converted to 'i' (dotted i) in text input fields.
class TurkishTextInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // No need to modify the text, just ensure it's preserved as entered
    return newValue;
  }
}

/// Extension methods for String to handle Turkish character conversions
extension TurkishStringExtension on String {
  /// Converts a string to lowercase, preserving Turkish characters
  String toTurkishLowerCase() {
    return replaceAll('I', 'ı')
        .replaceAll('İ', 'i')
        .toLowerCase();
  }

  /// Converts a string to uppercase, preserving Turkish characters
  String toTurkishUpperCase() {
    return replaceAll('i', 'İ')
        .replaceAll('ı', 'I')
        .toUpperCase();
  }
}
