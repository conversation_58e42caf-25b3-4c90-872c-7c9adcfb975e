import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fin_notebook/providers/transaction_provider.dart';
import 'package:fin_notebook/providers/category_provider.dart';
import 'package:fin_notebook/providers/account_provider.dart';
import 'package:fin_notebook/providers/recurring_transaction_provider.dart';
import 'package:fin_notebook/providers/auth_provider.dart';
import 'package:fin_notebook/providers/bank_statement_provider.dart';
import 'package:fin_notebook/screens/splash_screen.dart';
import 'package:fin_notebook/utils/logger.dart';
import 'package:logging/logging.dart';

void main() {
  // Initialize logger
  AppLogger.init(level: Level.ALL);

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()..initAuth()),
        ChangeNotifierProvider(create: (_) => CategoryProvider()),
        ChangeNotifierProvider(create: (_) => AccountProvider()),
        ChangeNotifierProvider(create: (_) => TransactionProvider()),
        ChangeNotifierProvider(create: (_) => RecurringTransactionProvider()),
        ChangeNotifierProvider(create: (_) => BankStatementProvider()),
      ],
      child: MaterialApp(
        title: 'Finance Notebook',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.green),
          useMaterial3: true,
        ),
        home: const SplashScreen(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}


