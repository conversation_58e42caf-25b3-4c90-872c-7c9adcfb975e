class BankStatementEntry {
  final String date;
  final String description;
  final double amount;
  final String type; // "income" or "expense"
  final String? categoryId; // Auto-assigned category ID
  final String? accountId; // Auto-assigned account ID
  final bool isSelected;

  BankStatementEntry({
    required this.date,
    required this.description,
    required this.amount,
    required this.type,
    this.categoryId,
    this.accountId,
    this.isSelected = false,
  });

  BankStatementEntry copyWith({
    String? date,
    String? description,
    double? amount,
    String? type,
    String? categoryId,
    String? accountId,
    bool? isSelected,
  }) {
    return BankStatementEntry(
      date: date ?? this.date,
      description: description ?? this.description,
      amount: amount ?? this.amount,
      type: type ?? this.type,
      categoryId: categoryId ?? this.categoryId,
      accountId: accountId ?? this.accountId,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  Map<String, dynamic> toTransactionMap() {
    return {
      'title': description,
      'type': type,
      'amount': amount.abs(),
      'currency': 'TRY',
      'transaction_date': date,
      'category_id': categoryId, // Include the auto-assigned category ID
      'account_id': accountId, // Include the auto-assigned account ID
    };
  }

  @override
  String toString() {
    return 'BankStatementEntry{date: $date, description: $description, amount: $amount, type: $type, categoryId: $categoryId, accountId: $accountId, isSelected: $isSelected}';
  }
}
