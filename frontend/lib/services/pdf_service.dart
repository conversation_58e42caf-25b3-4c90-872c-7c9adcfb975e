import 'dart:convert';
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:fin_notebook/utils/logger.dart';
import 'package:http/http.dart' as http;
import 'package:path/path.dart' as path;
import 'package:shared_preferences/shared_preferences.dart';

class PdfService {
  /// Picks a PDF file from device storage
  Future<File?> pickPdfFile() async {
    try {
      // Use the file picker
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
      );

      if (result != null && result.files.single.path != null) {
        return File(result.files.single.path!);
      }
      return null;
    } catch (e) {
      AppLogger.error('Error picking PDF file: $e', e);
      return null;
    }
  }

  /// Uploads a PDF file to the server for processing
  Future<Map<String, dynamic>?> uploadPdfForProcessing(File pdfFile) async {
    try {
      AppLogger.info('Uploading PDF file for processing: ${pdfFile.path}');

      // Get the auth token
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      // Create multipart request
      final url = Uri.parse('http://localhost:8008/api/v1/bank-statements/upload');
      final request = http.MultipartRequest('POST', url);

      // Add authorization header
      request.headers['Authorization'] = 'Bearer $token';

      // Add file to request
      final fileStream = http.ByteStream(pdfFile.openRead());
      final fileLength = await pdfFile.length();

      AppLogger.info('PDF file size: $fileLength bytes');

      final multipartFile = http.MultipartFile(
        'file',
        fileStream,
        fileLength,
        filename: path.basename(pdfFile.path),
      );

      request.files.add(multipartFile);

      // Send request
      AppLogger.info('Sending PDF upload request to server');
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      // Check response
      AppLogger.info('Server response status code: ${response.statusCode}');

      if (response.statusCode == 200) {
        final parsedResponse = _parseResponse(response.body);

        // Log the response data
        if (parsedResponse != null && parsedResponse['data'] != null) {
          final data = parsedResponse['data'];
          if (data['entries'] != null) {
            final entries = data['entries'] as List;
            AppLogger.info('Received ${entries.length} entries from server');
          } else {
            AppLogger.warning('No entries found in server response');
          }
        } else {
          AppLogger.warning('Invalid response format from server');
        }

        return parsedResponse;
      } else {
        AppLogger.error('Server returned error: ${response.body}');
        throw Exception('Failed to upload PDF: ${response.body}');
      }
    } catch (e) {
      AppLogger.error('Error uploading PDF for processing: $e', e);
      return null;
    }
  }

  /// Parses the response from the server
  Map<String, dynamic>? _parseResponse(String responseBody) {
    try {
      AppLogger.info('Parsing response: $responseBody');

      final decoded = jsonDecode(responseBody);
      if (decoded is Map<dynamic, dynamic>) {
        return Map<String, dynamic>.from(decoded);
      } else {
        AppLogger.error('Response is not a map: $decoded');
        return null;
      }
    } catch (e) {
      AppLogger.error('Error parsing response: $e', e);
      return null;
    }
  }
}
