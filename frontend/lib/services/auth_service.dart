import 'package:shared_preferences/shared_preferences.dart';
import 'package:fin_notebook/services/api_service.dart';
import 'package:fin_notebook/utils/logger.dart';

class AuthService {
  final ApiService _apiService = ApiService();

  // Register a new user
  Future<bool> register({
    required String name,
    required String username,
    required String email,
    required String password,
    required String passwordConfirmation,
  }) async {
    try {
      await _apiService.post('/register', data: {
        'name': name,
        'username': username,
        'email': email,
        'password': password,
        'password_confirmation': passwordConfirmation,
      });

      AppLogger.info('User registered successfully: $username');
      return true;
    } catch (e) {
      AppLogger.error('Registration error: $e', e);
      rethrow;
    }
  }

  // Login user and store token
  Future<Map<String, dynamic>> login({required String username, required String password}) async {
    try {
      final response = await _apiService.post('/login', data: {
        'username': username,
        'password': password,
      });

      if (response != null && response['token'] != null) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('auth_token', response['token']);

        // Store user info if available
        if (response['user'] != null) {
          final user = response['user'] as Map<String, dynamic>;
          await prefs.setString('user_id', user['id'].toString());
          await prefs.setString('user_name', user['name'].toString());
          await prefs.setString('user_email', user['email'].toString());
        }

        AppLogger.info('User logged in successfully: $username');
        return response;
      }

      AppLogger.warning('Login failed: Invalid response format');
      throw Exception('Invalid login response');
    } catch (e) {
      AppLogger.error('Login error: $e', e);
      rethrow;
    }
  }

  // Logout user
  Future<void> logout() async {
    try {
      // Call logout endpoint if your API has one
      try {
        await _apiService.post('/logout');
      } catch (e) {
        // Ignore errors from the logout endpoint
        AppLogger.warning('Error calling logout endpoint: $e');
      }

      // Clear local storage regardless of API response
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('auth_token');
      await prefs.remove('user_id');
      await prefs.remove('user_name');
      await prefs.remove('user_email');

      AppLogger.info('User logged out successfully');
    } catch (e) {
      AppLogger.error('Logout error: $e', e);
      rethrow;
    }
  }

  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        return false;
      }

      // Optionally validate token with the server
      try {
        await _apiService.get('/me');
        return true;
      } catch (e) {
        // If token is invalid, clear it and return false
        if (e.toString().contains('401')) {
          await prefs.remove('auth_token');
          return false;
        }
        // For other errors, assume token is still valid
        AppLogger.warning('Error validating token: $e');
        return true;
      }
    } catch (e) {
      AppLogger.error('Error checking login status: $e', e);
      return false;
    }
  }

  // Get current user info
  Future<Map<String, String?>> getCurrentUser() async {
    try {
      // Try to get user info from API first
      try {
        final response = await _apiService.get('/me');
        if (response != null) {
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString('user_id', response['id'].toString());
          await prefs.setString('user_name', response['name'].toString());
          await prefs.setString('user_email', response['email'].toString());

          return {
            'id': response['id'].toString(),
            'name': response['name'].toString(),
            'email': response['email'].toString(),
          };
        }
      } catch (e) {
        // If API call fails, fall back to stored preferences
        AppLogger.warning('Failed to get user from API, using stored data: $e');
      }

      // Get user info from shared preferences
      final prefs = await SharedPreferences.getInstance();
      return {
        'id': prefs.getString('user_id'),
        'name': prefs.getString('user_name'),
        'email': prefs.getString('user_email'),
      };
    } catch (e) {
      AppLogger.error('Error getting current user: $e', e);
      rethrow;
    }
  }

  // Refresh user profile
  Future<void> refreshUserProfile() async {
    try {
      final response = await _apiService.get('/me');

      if (response != null) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('user_id', response['id'].toString());
        await prefs.setString('user_name', response['name'].toString());
        await prefs.setString('user_email', response['email'].toString());

        AppLogger.info('User profile refreshed successfully');
      }
    } catch (e) {
      AppLogger.error('Error refreshing user profile: $e', e);
      rethrow;
    }
  }
}
